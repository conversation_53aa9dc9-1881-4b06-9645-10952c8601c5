/* eslint-disable camelcase */
// import { getDefaultVal } from 'components/common/InputLanguage/utils';
import TRANSLATE_KEY from 'messages/constant';
import { getTranslateMessage } from 'containers/Translate/util';
import {
  toConditionAPI,
  toConditionAPIFilterExport,
} from 'containers/Filters/utils';
import { safeParse, MAX_LNAME } from 'utils/common';

import { MAP_VALIDATE } from '../../../utils/web/validateForm';
import { getErrorMessageV2Translate } from '../../../utils/web/message';
import { initExportXMLFieldData } from '../../XMLExportField/utils';
import { getXMLExportParams } from '../ModalExport/utils';

const MAP_TITLE = {
  titlCurrentPage: getTranslateMessage(
    TRANSLATE_KEY._TITL_CURRENT_PAGE,
    'Current page',
  ),
  titlBetween: getTranslateMessage(TRANSLATE_KEY._TITL_BETWEEN, 'Between'),
  titlAllPage: getTranslateMessage(TRANSLATE_KEY._TITL_ALL_PAGE, 'All page'),
  lanelComma: getTranslateMessage(TRANSLATE_KEY._OPTION_COMMA, 'Comma'),
  lanelTab: getTranslateMessage(TRANSLATE_KEY._OPTION_TAB, 'Tab'),
};
export const DatimeDefault = today => {
  const getMonth =
    today.getMonth() + 1 >= 10
      ? `${today.getMonth() + 1}`
      : `0${today.getMonth() + 1}`;
  const getDate =
    today.getDate() >= 10 ? `${today.getDate()}` : `0${today.getDate()}`;
  const getYear = `${today.getFullYear()}`;
  const getHours =
    today.getHours() >= 10 ? `${today.getHours()}` : `0${today.getHours()}`;
  const getMinute =
    today.getMinutes() >= 10
      ? `${today.getMinutes()}`
      : `0${today.getMinutes()}`;
  const getSecond =
    today.getSeconds() >= 10
      ? `${today.getSeconds()}`
      : `0${today.getSeconds()}`;
  const dateTime = `${getMonth}_${getDate}_${getYear}_${getHours}_${getMinute}_${getSecond}`;
  return dateTime;
};
const formatOptions = [
  {
    label: '.xlsx',
    value: 'xlsx',
    display: 'Microsoft Excel (.xlsx)',
  },
  {
    label: '.xls',
    value: 'xls',
    display: 'Microsoft Excel (.xls)',
  },
  {
    label: '.csv',
    // translateCode: TRANSLATE_KEY._CHARACTER_SPACE,
    value: 'csv',
    display: 'Comma-separated values (.csv)',
  },
];
const optionDeliter = [
  {
    value: 'Comma',
    label: MAP_TITLE.lanelComma,
  },
  {
    value: 'Tab',
    label: MAP_TITLE.lanelTab,
  },
];
const initInputElement = placeHolderName => ({
  name: '',
  value: placeHolderName,
  maxLength: null,
  isRequired: true,
  label: '',
  error: [],
  isValidate: false,
  validate: () => ({ error: [], isValidate: false }),
});
export const initState = placeHolderName => ({
  optionDeliter,
  deliterType: optionDeliter[0],
  formatOptions,
  fileFormat: formatOptions[0],
  // compressFile: false,
  between: {
    from: 1,
    to: 1,
  },
  isDoing: false,
  type: 'only_info',
  inputFields: ['name'],
  name: {
    ...initInputElement(placeHolderName),
    name: 'name',
    maxLength: MAX_LNAME,
    label: 'Label',
    intro: 'Value',
    validate: MAP_VALIDATE.fieldNameExport,
  },
  isValidate: false,
  isLoading: false,
  exportXMLFlow: initExportXMLFieldData,
  // page: 1,
  // // initData.limit
  // limit: 25,
});
export function getColumnExport(column, main, type) {
  const columns = [];
  column.columnObj.columns.columnsAlias.forEach(each => {
    const { map } = main.groupAttributes;
    if (map[each] && map[each].type !== 2 && map[each].type !== 3) {
      columns.push(each);
    }
  });
  if (!columns.includes(type)) {
    columns.push(type);
  }
  return columns;
}
export function getPerColumn(column, main) {
  const perColumns = [];
  column.columnObj.columns.columnsAlias.forEach(each => {
    const { map } = main.groupAttributes;
    if (map[each] && (map[each].type === 2 || map[each].type === 3)) {
      perColumns.push(each);
    }
  });
  return perColumns;
}
export const toEntryAPI = data => {
  const {
    itemTypeId,
    paging,
    fileFormat,
    // compressFile,
    fileName,
    exportedRows,
    exportColumns,
    sort,
    filters,
    decryptFields,
    durations,
    deliterType,
    // properties,
    filterSegments,
    columns,
    object_type,
    perf_columns,
    channelId,
    storyId,
    getListType,
    sortDefault,
  } = data;
  let delimiter;
  let filterTemp;
  if (fileFormat.value === 'csv') {
    delimiter = deliterType.value === 'Comma' ? 'comma' : 'tab';
  }
  if (object_type === 'PROMOTION_POOL_CODE') {
    filterTemp = toConditionAPIFilterExport(
      filters.rules,
      filters.config.library.filterCustom,
    );
  } else {
    filterTemp = toConditionAPI(filters.rules);
  }
  // const orderedColumns = perf_columns ? columns.concat(perf_columns) : columns;
  let params = {
    // objectType: object_type,
    itemTypeId: parseInt(itemTypeId),
    fileName: fileName.replace(/ /g, '_'),
    fileFormat: fileFormat.value,
    // compressFile,
    filterSegments,
    // compressType: compressFile ? 'zip' : null,
    exportedRows,
    decryptFields,
    filters: filterTemp,
    sd: safeParse(sort.by, 'desc'),
    sort: safeParse(sort.key || sortDefault, ''),
    isAllColumns: exportColumns === 'all_info',
    perfColumns: perf_columns,
    // durations,
    delimiter,
    // channelId,
    // orderedColumns: exportColumns === 'all_info' ? {} : orderedColumns,
    limit: paging.limit,
    columns: exportColumns === 'all_info' ? {} : columns,
    // storyId,
  };
  if (getListType) {
    params.getListType = getListType;
  }
  // In case xml we need to overide some property: columns, orderedColumns.
  if (fileFormat.value === 'xml') {
    params = {
      ...params,
      ...getXMLExportParams({
        exportXMLFlow: data.XMLExportField,
        columns: params.columns,
      }),
    };
  }

  return params;
};

export const NOTI = {
  fail: res => ({
    id: 'edit-name-error',
    ...getErrorMessageV2Translate(res.codeMessage),
    timeout: 4000,
    timestamp: 1554173349265,
    type: 'danger',
  }),
  success: () => ({
    id: 'edit-name-success',
    message: `Save!`,
    translateCode: TRANSLATE_KEY._NOTIFICATION_SUCCESS,
    timeout: 4000,
    timestamp: 1554173349265,
    type: 'success',
  }),
  serviceNotFound: () => ({
    id: 'object-server-error',
    message: 'Service not found',
    timeout: 4000,
    timestamp: 1554173349265,
    type: 'danger',
  }),
};
