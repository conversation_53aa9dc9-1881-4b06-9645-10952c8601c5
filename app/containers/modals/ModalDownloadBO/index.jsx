/* eslint-disable no-useless-escape */
/* eslint-disable indent */
/* eslint-disable no-nested-ternary */
/* eslint-disable react/prop-types */
/* eslint-disable camelcase */
/* eslint-disable no-param-reassign */
import React, { useEffect } from 'react';
import { PropTypes } from 'prop-types';
import { useImmer } from 'use-immer';
import { connect } from 'react-redux';
import {
  UIModal,
  UIModalHeader,
  UIModalBody,
  UIModalFooter,
  UIInput,
  UICheckbox,
  // UIButton as Button,
  UILoading as Loading,
  UIWrapperDisable as WrapperDisable,
  UITextField,
} from '@xlab-team/ui-components';

import parse from 'html-react-parser';

import styled from 'styled-components';
import ModalFooter from 'components/Molecules/ModalFooter';

import Grid from '@material-ui/core/Grid';
import { addNotification } from 'redux/actions';
import { getTranslateMessage } from 'containers/Translate/util';
import TRANSLATE_KEY from 'messages/constant';

import RadioGroup from '@material-ui/core/RadioGroup';
import { Radio, FormControlLabel } from '@material-ui/core';
import FormControl from '@material-ui/core/FormControl';

import {
  initState,
  NOTI,
  toEntryAPI,
  DatimeDefault,
  getColumnExport,
  getPerColumn,
} from './utils';
import { getItemAttributeDecryptFields } from '../../../utils/web/attribute';
import useToggle from '../../../hooks/useToggle';
import APP from '../../../appConfig';
import { getPortalId } from '../../../utils/web/cookie';
import { Button, Flex, Modal, Select } from '@antscorp/antsomi-ui';
import { StyleModal } from './styled';
// import XMLExportField from '../../XMLExportField';

const MAP_TITLE = {
  xmlExportedColTitle: getTranslateMessage(
    TRANSLATE_KEY.KHONG_CO,
    'Include attribute available in CDP 365',
  ),
  titleSchema: getTranslateMessage(TRANSLATE_KEY.KHONG_CO, 'Schema'),
  actDowload: getTranslateMessage(TRANSLATE_KEY._ACT_DOWNLOAD, 'Download'),
  actDowloadData: getTranslateMessage(
    TRANSLATE_KEY._ACT_DOWNLOAD_DATA,
    'Download data',
  ),
  actCancel: getTranslateMessage(TRANSLATE_KEY._ACT_CANCEL, 'Cancel'),
  actExport: getTranslateMessage(TRANSLATE_KEY._ACT_EXPORT, 'Export data'),
  titlExport: getTranslateMessage(
    TRANSLATE_KEY._,
    'Prepare your download file',
  ),
  titleDelimiter: getTranslateMessage(
    TRANSLATE_KEY._TITL_DELIMITER_TYPE,
    'Delimiter type',
  ),
  titlFormat: getTranslateMessage(
    TRANSLATE_KEY._TITL_FILE_FORMAT,
    'File format',
  ),
  titlFileName: getTranslateMessage(TRANSLATE_KEY._TITL_FILE_NAME, 'File name'),
  titlPageDownload: getTranslateMessage(
    TRANSLATE_KEY._TITL_PAGE_DOWNLOAD,
    'Page download',
  ),
  titleExportRow: getTranslateMessage(
    TRANSLATE_KEY._TITL_EXPORTED_ROWS,
    'Exported rows',
  ),
  titleExportCol: getTranslateMessage(
    TRANSLATE_KEY._TITL_EXPORTED_COLUMNS,
    'Exported coulmns',
  ),
  titlCompressFile: getTranslateMessage(
    TRANSLATE_KEY._TITL_COMPRESS_FILE,
    'Compress file',
  ),
  titleExportHistory: getTranslateMessage(
    TRANSLATE_KEY._MENU_SUB_EXPORT_HISTORY,
    'Export History',
  ),
  noteFileName: getTranslateMessage(
    TRANSLATE_KEY._TITL_NOTE_EXPORT_FILE_NAME,
    'Use aiphabet letters (a-Z) & number (0-9) only',
  ),
  close: getTranslateMessage(TRANSLATE_KEY._ACT_CLOSE, 'Close'),
  titleIncluRow: x =>
    getTranslateMessage(
      TRANSLATE_KEY._TITL_EXPORTED_INCLUDE_ROWS,
      `Include ${x} row(s) in your list`,
      {
        x,
      },
    ),
  titlFromPage: getTranslateMessage(TRANSLATE_KEY._TITL_FROM_PAGE, 'From page'),
  titlToPage: getTranslateMessage(TRANSLATE_KEY._TITL_TO_PAGE, 'to page'),
};
const styleHeader = {
  color: '#000000',
  fontSize: '16px',

  // boxShadow: '0px 0px 3px grey',
};

export const ButtonStyle = styled(Button)`
  margin: 0rem 0.25rem;
`;
export const ContainerCustomize = styled.div`
  overflow: hidden;

  .MuiGrid-root {
    row-gap: 10px;
  }

  .title-name,
  .title-schema {
    position: relative;
    align-self: start;
    top: 16px;
    transform: translateY(-50%);
  }
`;

export const ModalFooterStyle = styled(ModalFooter)`
  justify-content: flex-end;
  padding-right: 2rem;
`;
export const RadioGroupStyle = styled(RadioGroup)`
  flex-direction: column !important ;
  //margin-top: 30px;
`;
export const UIInputStyle = styled(UIInput)`
  width: 100%;
`;
export const ContainerSelect = styled.div`
  display: flex;
  align-items: center;
`;
export const HeaderModalDowload = styled.div`
  color: rgb(0, 0, 0);
  font-size: 16px;
`;
export const TitleWarnDownload = styled.p`
  flex: none;
  margin: 0 16px 0 0;
  color: #666666;
  font-size: 12px;
`;
export const TitleWarnExport = styled.p`
  flex: none;
  margin: 0 20px 15px 0;
  color: rgb(0, 0, 0);
  font-size: 12px;
`;
export const ContainerCheckbox = styled.div`
  display: flex;
  align-items: center;
  margin: 8px 0;
  .label {
    font-size: 13px;
  }
`;
export const WrapperLayout = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  legend,
  .MuiTypography-root {
    font-size: 0.875rem !important;
  }

  .MuiFormGroup-row {
    margin-left: 4px;
  }
`;
export const WrraperTextSmall = styled.span`
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  margin: 5px 0px 0px 0px;
  color: #999999;

  // color: #fdc62e;
`;
export const LabelRadius = styled.p`
  font-size: 12px;
  line-height: 1px;
`;
function ModalDowload(props) {
  const {
    isOpen,
    toggle,
    paging,
    sort,
    filters,
    perf_columns,
    durations,
    filterSegments,
    // decryptFields,
    itemTypeId,
    columns,
    // properties,
    object_type,
    // feService,
    // otherData,
    channelId,
    ObjectServicesFn,
    objectName,
    storyId,
    getListType,
    sortDefault,
    id,
  } = props;
  const [state, setState] = useImmer(initState());
  const [isOpenModalDownload, toggleModalDownload] = useToggle(false);
  useEffect(() => {
    if (isOpen) {
      const today = new Date();
      const placeHolderName = `${objectName}_${DatimeDefault(today)}`;
      setState(draft => initState(placeHolderName));
      if (placeHolderName) {
        setState(draft => {
          const { isValidate } = draft.name.validate(placeHolderName);
          draft.name.isValidate = isValidate;
          draft.isValidate = draft.inputFields.every(
            each => draft[each].isValidate,
          );
        });
      }

      // console.log(state);
    }
  }, [isOpen]);
  const onCancel = () => {
    toggle(false);
  };
  const onClose = () => {
    toggleModalDownload();
  };
  const onApply = () => {
    setState(draft => {
      draft.isLoading = true;
    });
    const data = {
      fileName: state.name.value,
      fileFormat: state.fileFormat,
      deliterType: state.deliterType,
      // compressFile: state.compressFile,
      exportedRows: paging.totalRecord,
      exportColumns: state.type,
      itemTypeId,
      filterSegments,
      paging,
      decryptFields: getItemAttributeDecryptFields(itemTypeId),
      sort,
      sortDefault,
      filters,
      durations,
      channelId,
      storyId,
      // properties,
      columns: Array.isArray(columns)
        ? columns
        : getColumnExport(columns.column, columns.main, columns.type),
      // object_type,
      perf_columns: perf_columns
        ? getPerColumn(perf_columns.column, perf_columns.main)
        : perf_columns,
      getListType,
      // XMLExportField: state.exportXMLFlow,
    };
    const config = {
      fileName: `${state.name.value}.${state.fileFormat.value}`,
      fileType: state.fileFormat.value,
    };
    const params = { data: toEntryAPI(data), config };

    if (props.getListType) {
      // 1 owner  -- 2 // shared with me
      params.data.getListType = props.getListType;
    }
    if (id) {
      params.id = id;
    }
    const exportService = ObjectServicesFn;

    if (exportService) {
      exportService(params).then(res => {
        setState(draft => {
          draft.isLoading = false;
        });

        if (res.code === 200 || res.code === 201) {
          toggle(false);
          // toggleModalDownload();
        } else {
          const notification = NOTI.fail(res);
          props.addNotification(notification);
        }
      });
    }
  };

  const onChangeFormat = value => {
    setState(draft => {
      draft.fileFormat = state.formatOptions.find(item => item.value === value);
    });
  };

  const onChangeDelimiter = value => {
    setState(draft => {
      draft.deliterType = state.optionDeliter.find(
        item => item.value === value,
      );
    });
  };

  const onChangeGroupType = e => {
    setState(draft => {
      draft.type = e.target.value;
    });
  };
  const onChangeValue = value => {
    let valueTmp = value;

    // Check first character === space, convert delete space
    if (valueTmp.charAt(0) === ' ') {
      valueTmp = `${valueTmp}`.replace(/[ ]/g, '');
    }

    // Delete character special to ''
    const replaceCharacterSpecial = `${valueTmp}`.replace(
      /[-|`*;&\/\\#,+()$~%.'":*?<>{}@^=![]|]/g,
      '',
    );

    // Conver space to '_'
    const replaceSpace = `${replaceCharacterSpecial}`.replace(/[ ]/g, '_');

    setState(draft => {
      draft.name.value = replaceSpace;
      const { error, isValidate } = draft.name.validate(replaceSpace);
      draft.name.isValidate = isValidate;
      draft.name.error = error;
      draft.isValidate = draft.inputFields.every(
        each => draft[each].isValidate,
      );
    });
  };
  return (
    <>
      <StyleModal
        open={isOpen}
        onCancel={toggle}
        title={MAP_TITLE.actDowloadData}
        okText={MAP_TITLE.actDowload}
        onOk={onApply}
        okButtonProps={{
          disabled:
            state.isLoading ||
            paging.totalRecord === 0 ||
            !state.isValidate ||
            (state.fileFormat.value === 'xml' &&
              !state.exportXMLFlow.isValidXMLSchema),
        }}
        zIndex={1400}
        data-test="modal-download"
      >
        <WrapperDisable disabled={paging.totalRecord === 0}>
          <Loading isLoading={state.isLoading} />
          <ContainerCustomize>
            <Grid container alignItems="center">
              <Grid item xs={4}>
                <TitleWarnDownload style={{ marginBottom: '15px' }}>
                  {MAP_TITLE.titlFileName}
                </TitleWarnDownload>
              </Grid>
              <Grid item xs={8}>
                <UITextField
                  textFieldProps={{
                    size: 'small',
                    multiline: false,
                    rowsMax: 1,
                    className: 'width-100',
                    id: 'standard-basic',
                    error: !!state.name.error[0],
                    // label: 'Use aiphabet letters (a-Z) & number (0-9) only',
                  }}
                  value={state.name.value}
                  onChange={onChangeValue}
                  // maxLength={state.name.maxLength}
                  firstText={state.name.error}
                />
                <WrraperTextSmall
                  style={{ display: !state.isValidate ? 'none' : '' }}
                >
                  {MAP_TITLE.noteFileName}
                </WrraperTextSmall>
              </Grid>
              <Grid item xs={4}>
                <TitleWarnDownload style={{ marginBottom: '15px' }}>
                  {MAP_TITLE.titlFormat}
                </TitleWarnDownload>
              </Grid>
              <Grid style={{ marginBottom: '10px' }} item xs={8}>
                <Select
                  options={state.formatOptions}
                  onChange={onChangeFormat}
                  value={state.fileFormat}
                />
                {/* <UISelect
                  isSearchable={false}
                  use="tree"
                  options={state.formatOptions}
                  value={state.fileFormat}
                  onChange={onChangeFormat}
                /> */}
                {state.fileFormat.value === 'xlsx' ? (
                  <WrraperTextSmall>
                    {getTranslateMessage(
                      TRANSLATE_KEY._TITL_NOTE_MAX_EXPORT,
                      '.xlsx file only allows exporting a maximum of 50,000 rows',
                    )}
                  </WrraperTextSmall>
                ) : (
                  <></>
                )}
              </Grid>
              {state.fileFormat.value === 'csv' ? (
                <>
                  <Grid item xs={4}>
                    <TitleWarnDownload>
                      {MAP_TITLE.titleDelimiter}
                    </TitleWarnDownload>
                  </Grid>
                  <Grid style={{ marginBottom: '10px' }} item xs={8}>
                    <Select
                      options={state.optionDeliter}
                      onChange={onChangeDelimiter}
                      value={state.deliterType}
                    />
                  </Grid>
                </>
              ) : (
                <></>
              )}
              <Grid item xs={4}>
                <TitleWarnDownload>
                  {MAP_TITLE.titleExportRow}
                </TitleWarnDownload>
              </Grid>
              <Grid item xs={8} style={{ marginTop: '6px' }}>
                <TitleWarnDownload style={{ color: '#000000' }}>
                  {MAP_TITLE.titleIncluRow(paging.totalRecord)}
                </TitleWarnDownload>
              </Grid>
              <Grid container item xs={12}>
                <Grid item xs={4} style={{ marginTop: '10px' }}>
                  <TitleWarnDownload>
                    {MAP_TITLE.titleExportCol}
                  </TitleWarnDownload>
                </Grid>
                <Grid item xs={8} style={{ marginTop: '6px' }}>
                  {state.fileFormat.value === 'xml' ? (
                    <TitleWarnDownload>
                      {MAP_TITLE.xmlExportedColTitle}
                    </TitleWarnDownload>
                  ) : (
                    <WrapperLayout>
                      <FormControl>
                        <RadioGroupStyle
                          onChange={onChangeGroupType}
                          value={state.type}
                          name="trigger-type"
                        >
                          <FormControlLabel
                            value="only_info"
                            control={
                              <Radio
                                color="primary"
                                size="small"
                                checked={state.type === 'only_info'}
                              />
                            }
                            label={
                              <LabelRadius>
                                {getTranslateMessage(
                                  TRANSLATE_KEY._EXPORTED_COLUMNS_ONLY_INFO,
                                  'Only info being displayed',
                                )}
                              </LabelRadius>
                            }
                          />
                          <FormControlLabel
                            value="all_info"
                            control={
                              <Radio
                                color="primary"
                                size="small"
                                checked={state.type === 'all_info'}
                                // disabled
                              />
                            }
                            label={
                              <LabelRadius>
                                {getTranslateMessage(
                                  TRANSLATE_KEY._EXPORTED_COLUMNS_ALL_INFO,
                                  'All info',
                                )}
                              </LabelRadius>
                            }
                          />
                        </RadioGroupStyle>
                      </FormControl>
                    </WrapperLayout>
                  )}
                </Grid>
              </Grid>
            </Grid>
          </ContainerCustomize>
        </WrapperDisable>
      </StyleModal>

      <Modal
        open={isOpenModalDownload}
        title={getTranslateMessage(
          TRANSLATE_KEY._TITL_PROCESSING_YOUR_FILE,
          'Processing your file',
        )}
        footer={null}
        onCancel={onClose}
      >
        <Flex vertical gap={20}>
          <div>
            {parse(
              getTranslateMessage(
                TRANSLATE_KEY._WARN_EXPORT_PROCESSING,
                `The system is processing your file, please visit <a href ="${
                  APP.PREFIX
                }/${getPortalId()}/api-hub/export-history" target="_blank">Export History </a> to check the exporting status and download the file.<br><br>Also, a notification will be sent to you when the file exported`,
                {
                  x: `<a href="${
                    APP.PREFIX
                  }/${getPortalId()}/api-hub/export-history" target="_blank"  >${
                    MAP_TITLE.titleExportHistory
                  }</a>`,
                },
              ),
            )}
          </div>
          <div>
            <Button onClick={onClose}>{MAP_TITLE.close}</Button>
          </div>
        </Flex>
      </Modal>
    </>
  );
}

function mapDispatchToProps(dispatch) {
  return {
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
  };
}

ModalDowload.defaultProps = {
  isOpen: false,
  otherData: {},
};

ModalDowload.propTypes = {
  isOpen: PropTypes.bool,
  otherData: PropTypes.object,
};
export default connect(
  null,
  mapDispatchToProps,
)(ModalDowload);
