/* eslint-disable react/prop-types */
import React, { useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import iphoneImg from 'assets/images/simulator/zalo-zns-v2.png';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';

import {
  makeSelectDashboardNetworkInfo,
  makeSelectPersonalizations,
} from '../../modules/Dashboard/selector';
import DestinationServices from 'services/Destination';
import {
  handleConvertContentHTML,
  processTemplateToEl,
  replaceNbsps,
} from './utils';
import { useDeepCompareEffect } from '../../hooks';
import { UILoading } from '@xlab-team/ui-components';
import { isEmpty } from 'lodash';

const Wrapper = styled.div`
  position: absolute;
  top: 8px;
  left: 27px;
  width: 333px;
  /* width: 345px; */
  #template-preview {
    display: flex;

    .preview-container {
      padding: 0;
      margin: 0;
      box-sizing: border-box;
    }
  }
`;

const WrapperZaloZNSPreview = styled.div`
  position: relative;
  padding: 15px 0;
  overflow: hidden;
  height: 100%;
  width: fit-content;
  margin: auto;
`;

const ContainTextWrapper = styled.div`
  height: 580px;
  width: 100%;
  top: 123px;
  position: absolute;
  overflow: auto;
  overflow-x: hidden;
`;

const ContainInfoNetwork = styled.div`
  position: absolute;
  width: 100%;
  top: 80px;
  display: flex;

  .portal-name {
    font-size: 14px;
    font-weight: bold;
    margin-top: 5px;
    justify-content: flex-start;
    flex-direction: column;
    left: 65px;
    color: #fff;
    .status {
      font-size: 11px;
      color: #fff;
      font-weight: normal;
    }
  }

  .portal-name,
  .logo-contain {
    display: flex;
    position: absolute;
    width: 100%;
  }
`;

const ZaloZNS = props => {
  const { template, templateData } = props.data;
  const refContain = useRef(null);
  const [htmlContent, setHtmlContent] = useState('');
  const [listKey, setListKey] = useState([]);

  const { networkName } = props.networkInfo;

  const {
    settings: { personalizationData = {} },
  } = props.personalizations || {};

  const templateId =
    template?.value?.value || (!isNaN(template?.value) ? template.value : '');

  useDeepCompareEffect(() => {
    if (props.destinationId && templateId) {
      DestinationServices.info
        .getDetailFormZNS({
          destinationId: props.destinationId,
          templateId,
        })
        .then(res => {
          if (res.code === 200 && !isEmpty(res.data)) {
            const contentHtml = res.data.previewUrlDescription;
            const newlistKey = res.data.listParams.map(params => params.name);
            setListKey(newlistKey);
            // handle content before load content HTML
            let contentConvertDisplay = contentHtml;
            if (contentConvertDisplay) {
              contentConvertDisplay = handleConvertContentHTML(contentHtml);
            }
            processTemplateToEl({
              el: refContain.current,
              template: contentConvertDisplay,
              isCapture: props.isCapture,
              listKey: newlistKey,
              onUpdateDone: processedHTMLContent => {
                setHtmlContent(processedHTMLContent);
              },
            });
          }
        })
        .catch(e => {
          console.log(e);
        });
    }

    return () => {
      setHtmlContent('');
      setListKey([]);
    };
  }, [props.destinationId, templateId]);

  useEffect(() => {
    if (!htmlContent) return;

    if (Object.keys(templateData.value || {}).length) {
      handleSetContentEl(templateData.value);
    } else if (
      templateData.initValue &&
      Object.keys(templateData.initValue).every(key => listKey.includes(key))
    ) {
      handleSetContentEl(templateData.initValue);
    }
  }, [templateData, listKey, htmlContent]);

  const handleSetContentEl = data => {
    Object.keys(data).forEach(key => {
      const elDynamicText = refContain.current.querySelector(`.${key}`);

      if (elDynamicText) {
        const innerText =
          replaceNbsps(data[key], personalizationData) || `{${key}}`;

        elDynamicText.innerText = innerText;
      }
    });
  };

  return (
    <WrapperZaloZNSPreview>
      {!htmlContent && <UILoading isLoading />}
      <ContainInfoNetwork>
        <div className="portal-name">
          <span>{networkName}</span>
          <span className="status">Official Account</span>
        </div>
      </ContainInfoNetwork>
      <img alt="preview" src={iphoneImg} />
      <ContainTextWrapper>
        <Wrapper>
          <div id="template-preview" ref={refContain} />
        </Wrapper>
      </ContainTextWrapper>
    </WrapperZaloZNSPreview>
  );
};

const mapStateToProps = createStructuredSelector({
  networkInfo: makeSelectDashboardNetworkInfo(),
  personalizations: makeSelectPersonalizations(),
});

export default connect(
  mapStateToProps,
  null,
)(ZaloZNS);
