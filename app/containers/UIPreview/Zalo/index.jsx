/* eslint-disable indent */
/* eslint-disable react/prop-types */
// Libraries
import React from 'react';
import PropTypes from 'prop-types';
import { cloneDeep, isEmpty } from 'lodash';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';

// Components
import ErrorBoundary from 'components/common/ErrorBoundary';
import IconXlab from 'components/common/UIIconXlab';

// Assets
import SkeletonImg from './images/skelaton.png';
import LogoAntsomi from './images/logo-antsomi.png';
import TranslateImg from './images/translate.png';
import MegaIcon from './images/megaphone.png';
import NoImage from '../../../images/no-media.png';
// Styled
import {
  PreviewContainer,
  WrapperImagePreview,
  LimitImage,
  PortalName,
  BoxText,
  BoxImage,
  BoxImagePreview,
  TextPreview,
  StickerBox,
  BoxMessage,
  HeadingPinned,
  Heading,
  Text,
  ContentMessage,
  ListButtons,
  ItemButton,
  ListKeyValue,
  KeyValueItem,
  Item,
  PreviewTextTemplate,
  ScrollBox,
} from './styled';

// Constants
import {
  CATALOG_CODES,
  TEMPLATE_ZALO_OA_TYPES,
} from '../../../modules/Dashboard/MarketingHub/Destination/CreateV2/Design/Templates/constants';
import { STICKER_FACTORY } from '../../../components/Molecules/Sticker/constants';

// Translations
import { getTranslateMessage } from '../../Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';

// Utils
import { getObjectPropSafely } from '../../../utils/common';
import { buildStickerLinkZaloOA } from '../../../components/Molecules/Sticker/utils';
import { replaceNbsps } from '../utils';

// Selectors
import { makeSelectPersonalizations } from '../../../modules/Dashboard/selector';
import { ImagePreview } from '@antscorp/antsomi-ui';

const PATH = 'app/containers/UIPreview/Zalo/index.jsx';

const textPreviewStyles = {
  fontSize: '13px',
  fontWeight: 'bold',
  lineHeight: '16px',
  maxHeight: 32,
  marginBottom: 10,
  display: '-webkit-box',
  WebkitLineClamp: 2,
  WebkitBoxOrient: 'vertical',
  overflow: 'hidden',
  padding: '0px 15px',
  borderTop: 'none',
};

const styleBtn = {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  overflow: 'hidden',
  flex: 1,
  fontSize: '13px',
};
const labelBtnStyle = {
  color: '#000000',
  whiteSpace: 'nowrap',
  textOverflow: 'ellipsis',
  overflow: 'hidden',
};

const styleKey = {
  color: 'rgba(88, 88, 88, 1)',
  fontWeight: '700',
  textOverflow: 'ellipsis',
  whiteSpace: 'nowrap',
  overflow: 'hidden',
};
const styleValue = {
  color: 'rgba(66, 64, 64, 1)',
  fontWeight: '400',
  alignSelf: 'flex-start',
  textOverflow: 'ellipsis',
  whiteSpace: 'nowrap',
  overflow: 'hidden',
};

const styledNoti = {
  display: 'block',
  color: '#767676',
  fontSize: '12px',
  padding: '10px 4px 20px',
};

function ZaloPreview(props) {
  const { data } = props;
  const { template = {} } = data;

  const {
    settings: { personalizationData = {} },
  } = props.personalizations || {};

  const templateId = getObjectPropSafely(() => {
    const temp = template && template.value;
    if (isEmpty(temp)) return null;

    if (typeof temp === 'object') return temp.value;

    return temp;
  });

  const portalName = getObjectPropSafely(() => {
    let name;
    // eslint-disable-next-line no-undef
    if (!isEmpty(APP_CACHE_PARAMS)) {
      // eslint-disable-next-line no-undef
      name = APP_CACHE_PARAMS.p_name;
    }
    return name;
  });

  const renderKeyValueList = (dataList = [], itemType = {}, isEN = false) => {
    if (!Array.isArray(dataList) || dataList.length === 0) return null;
    const list = cloneDeep(dataList);
    const { key: keyFirst = '', value: valueFirst = '' } = list.shift() || {};
    const defaultTxtKey = isEN ? 'Label' : 'Nhãn';
    const defaultTxtValue = isEN ? 'Content' : 'Nội dung';

    const content = list.map(({ key = '', value = '' }, index) => (
      // eslint-disable-next-line react/no-array-index-key
      <KeyValueItem key={`${key}-${value}-${index}`}>
        <Item style={styleKey}>{key || defaultTxtKey}</Item>
        <Item style={styleValue}>{value || defaultTxtValue}</Item>
      </KeyValueItem>
    ));

    return (
      <ListKeyValue>
        <KeyValueItem>
          <Item style={styleKey}>{keyFirst || defaultTxtKey}</Item>
          <Item style={{ ...styleValue }}>{valueFirst || defaultTxtValue}</Item>
        </KeyValueItem>
        {!isEmpty(itemType) && (
          <KeyValueItem>
            <Item style={styleKey}>{isEN ? 'Message Type' : 'Loại tin'}</Item>
            <Item style={{ ...styleValue, fontWeight: 'bold' }}>
              {isEN ? itemType[0] : itemType[1]}
            </Item>
          </KeyValueItem>
        )}
        {content}
      </ListKeyValue>
    );
  };

  const renderListButtonMessage = (list = []) => {
    if (!Array.isArray(list) || list.length === 0) return null;

    const content = list.map((each, index) => {
      const { iconUrl = '', btnLabel = '' } = each;

      return (
        // eslint-disable-next-line react/no-array-index-key
        <ItemButton key={index}>
          <div style={{ width: 50 }}>
            <div style={{ width: 24, height: 24, overflow: 'hidden' }}>
              {iconUrl ? (
                <ImagePreview
                  src={iconUrl}
                  alt="button preview"
                  imgError={NoImage}
                  style={{ objectFit: iconUrl ? 'contain' : 'cover' }}
                />
              ) : (
                <div style={{ width: 24, height: 24, background: '#ffffff' }} />
              )}
            </div>
          </div>
          <div style={styleBtn}>
            <span style={labelBtnStyle}>
              {btnLabel || `Button ${index + 1}`}
            </span>
            <IconXlab
              name="arrow-right"
              fontSize="28px"
              color="rgba(88, 88, 88, 1)"
            />
          </div>
        </ItemButton>
      );
    });

    return <ListButtons>{content}</ListButtons>;
  };

  const renderPreviewTemplate = (tempId = '') => {
    switch (tempId) {
      case TEMPLATE_ZALO_OA_TYPES.TEXT: {
        const text = getObjectPropSafely(() => data.text.value);
        return (
          <BoxText>
            <PreviewTextTemplate>
              {replaceNbsps(text, personalizationData)}
            </PreviewTextTemplate>
          </BoxText>
        );
      }
      case TEMPLATE_ZALO_OA_TYPES.TRANSACTION:
      case TEMPLATE_ZALO_OA_TYPES.RICH_MEDIA: {
        const isTransaction = tempId === TEMPLATE_ZALO_OA_TYPES.TRANSACTION;

        const bannerUrl = getObjectPropSafely(
          () => data[isTransaction ? 'transactionBanner' : 'mediaBanner'].value,
        );
        const title = getObjectPropSafely(
          () => data[isTransaction ? 'transactionTitle' : 'mediaTitle'].value,
        );
        const subTitle = getObjectPropSafely(
          () =>
            data[isTransaction ? 'transactionSubtitle' : 'mediaSubtitle'].value,
        );
        const message = getObjectPropSafely(
          () =>
            data[isTransaction ? 'transactionMessage' : 'mediaMessage'].value,
        );
        const contentTable = getObjectPropSafely(
          () =>
            data[
              isTransaction ? 'transactionContentTable' : 'mediaContentTable'
            ].value,
        );
        const buttonActions = getObjectPropSafely(
          () =>
            data[
              isTransaction ? 'transactionButtonActions' : 'mediaButtonActions'
            ].value,
        );
        const { value: valueMessage = {}, options = [] } = getObjectPropSafely(
          () => data.transactionType || {},
        );

        const { flag = '', value: valueTmp } = valueMessage || {};

        const itemType = [];
        if (Array.isArray(options)) {
          const item = options.find(each => each.value === valueTmp);

          if (item) {
            const { label = '' } = item;
            itemType.push(...(label.split('-') || []));
          }
        }
        const isEN = flag === 'EN';

        return (
          <ScrollBox>
            <BoxMessage>
              <HeadingPinned>
                <ImagePreview
                  src={isTransaction ? TranslateImg : MegaIcon}
                  alt="Translate"
                  style={{ width: 20, height: 20, objectFit: 'cover' }}
                />
                <Heading>
                  {isTransaction
                    ? getTranslateMessage(
                        TRANSLATE_KEY._,
                        'TRANSACTIONAL MESSAGE',
                      )
                    : getTranslateMessage(
                        TRANSLATE_KEY._,
                        'RICH MEDIA MESSAGE',
                      )}
                </Heading>
              </HeadingPinned>
              <BoxImagePreview style={{ height: 217 }}>
                <ImagePreview
                  src={bannerUrl || LogoAntsomi}
                  alt="preview image template"
                  imgError={NoImage}
                  style={{ objectFit: bannerUrl ? 'contain' : 'cover' }}
                />
              </BoxImagePreview>
              <ContentMessage>
                <Text isBold lineClamp={2}>
                  {title || (isEN || !isTransaction ? 'Title' : 'Tiêu đề')}
                </Text>
                <Text lineClamp={3}>
                  {subTitle || (isEN || !isTransaction ? 'Subtitle' : 'Phụ đề')}
                </Text>
                {renderKeyValueList(
                  contentTable,
                  itemType,
                  isTransaction ? isEN : true,
                )}
                <Text lineClamp={2}>
                  {message ||
                    (isEN || !isTransaction ? 'Message' : 'Nội dung phụ')}
                </Text>
              </ContentMessage>
              {renderListButtonMessage(buttonActions)}
            </BoxMessage>
          </ScrollBox>
        );
      }
      case TEMPLATE_ZALO_OA_TYPES.IMAGE:
      case TEMPLATE_ZALO_OA_TYPES.REQUEST_INFO: {
        let content = '';
        let imageUrl = '';
        let title = '';
        const isTemplateImage = templateId === TEMPLATE_ZALO_OA_TYPES.IMAGE;

        if (isTemplateImage) {
          content = getObjectPropSafely(() => data.imgContent.value);
          imageUrl = getObjectPropSafely(() => data.imgUrl.value);
        } else {
          title = getObjectPropSafely(() => data.requestTitle.value);
          content = getObjectPropSafely(() => data.requestSubtitle.value);
          imageUrl = getObjectPropSafely(() => data.requestImageUrl.value);
        }

        return (
          <BoxImage>
            <BoxImagePreview>
              <ImagePreview
                src={imageUrl || LogoAntsomi}
                alt="preview image template"
                imgError={NoImage}
                style={{ objectFit: imageUrl ? 'contain' : 'cover' }}
              />
            </BoxImagePreview>
            <div
              style={
                !isTemplateImage
                  ? {
                      paddingTop: 10,
                      borderTop: '1px solid rgba(210, 210, 210, 1)',
                    }
                  : {}
              }
            >
              {!isTemplateImage && (
                <TextPreview style={textPreviewStyles}>
                  {title || 'Title'}
                </TextPreview>
              )}
              <TextPreview
                style={
                  !isTemplateImage
                    ? {
                        borderTop: 'none',
                        padding: '0px 15px',
                        margin: '0px 0px 10px',
                      }
                    : { padding: '10px 15px 0px', marginBottom: 10 }
                }
              >
                {content || 'Content'}
              </TextPreview>
            </div>
          </BoxImage>
        );
      }
      case TEMPLATE_ZALO_OA_TYPES.STICKER: {
        const sticker = getObjectPropSafely(() => data.sticker.value);
        const stickerSet = getObjectPropSafely(() => data.stickerType.value);

        if (isEmpty(stickerSet)) return null;

        let setTmp = stickerSet;
        let stickerUrl = null;
        if (typeof stickerSet === 'object') {
          setTmp = stickerSet.value;
        }

        const arrSticker = STICKER_FACTORY[CATALOG_CODES.ZALO_OA][setTmp];

        if (!isEmpty(arrSticker)) {
          const item = arrSticker.find(each => each.id === sticker);

          if (!isEmpty(item)) {
            const { index = '', version = '' } = item;

            stickerUrl = buildStickerLinkZaloOA({ index, version });
          }
        }

        return (
          <StickerBox>
            <ImagePreview
              src={stickerUrl || LogoAntsomi}
              alt="sticker"
              imgError={NoImage}
              style={{ objectFit: stickerUrl ? 'contain' : 'cover' }}
            />
          </StickerBox>
        );
      }
      default: {
        return null;
      }
    }
  };

  return (
    <ErrorBoundary path={PATH}>
      {templateId === TEMPLATE_ZALO_OA_TYPES.RICH_MEDIA && (
        <span style={styledNoti}>
          {getTranslateMessage(
            TRANSLATE_KEY._ZALO_RICH_TIME_LIMIT_DES,
            `This template could only be sent from 06:00 AM to 10:00 PM everyday. To prevent sending message unsuccessfully, make sure you choose the appropriate time range in Step 1 - Settings.`,
          )}
        </span>
      )}
      <PreviewContainer>
        {renderPreviewTemplate(templateId)}
        <PortalName>{portalName}</PortalName>
        <LimitImage>
          <WrapperImagePreview>
            <ImagePreview
              src={SkeletonImg}
              alt="skelaton preview"
              imgError={NoImage}
            />
          </WrapperImagePreview>
        </LimitImage>
      </PreviewContainer>
    </ErrorBoundary>
  );
}

ZaloPreview.defaultProps = {
  data: {},
};
ZaloPreview.propTypes = {
  data: PropTypes.object,
};

const mapStateToProps = createStructuredSelector({
  personalizations: makeSelectPersonalizations(),
});

export default connect(
  mapStateToProps,
  null,
)(ZaloPreview);
