/* eslint-disable react/prop-types */

//Libraries
import React, { useMemo } from 'react';
import styled from 'styled-components';
import { Flex, Typography } from '@antscorp/antsomi-ui';
import { map } from 'lodash';
import { translate, translations } from '@antscorp/antsomi-locales';

const Wrapper = styled.div`
  margin-top: 20px;
  border-radius: 10px;
  border: 1px solid #e5e5e5;
`;

const WrapperZaloPayZNSPreview = styled(Flex)`
  flex-direction: column;
  gap: 10px;

  padding: 15px;
  height: 100%;
  width: 100%;
`;

const ZNSPreview = props => {
  const { data, isSurroundLabel = true } = props;
  const templateData = data.templateData?.value || {};

  const convertDataToArray = useMemo(
    () =>
      map(templateData, (value, key) => ({
        label: isSurroundLabel ? `{${key}}` : key,
        value,
      })),
    [templateData, isSurroundLabel],
  );

  const renderItemPreview = ({ label, value, isLabel } = {}) => {
    return (
      <Flex gap={10}>
        <Typography.Paragraph
          ellipsis={{ tooltip: true, rows: 2 }}
          style={{
            flex: 1,
            margin: 0,
            maxHeight: '33.3px',
            color: isLabel ? '#7f7f7f' : undefined,
          }}
        >
          {label}
        </Typography.Paragraph>
        <Typography.Paragraph
          ellipsis={{ tooltip: true, rows: 4 }}
          style={{ flex: 1, margin: 0, color: isLabel ? '#7f7f7f' : undefined }}
        >
          {value}
        </Typography.Paragraph>
      </Flex>
    );
  };

  return (
    <Wrapper>
      <WrapperZaloPayZNSPreview>
        {renderItemPreview({
          label: translate(
            translations._SOURCE_JSON_TAB_REQUEST_CONFIG_KEY,
            'Key',
          ),
          value: translate(
            translations._SOURCE_JSON_TAB_REQUEST_CONFIG_VALUE,
            'Value',
          ),
          isLabel: true,
        })}

        <Flex vertical gap={10}>
          {convertDataToArray.map(renderItemPreview)}
        </Flex>
      </WrapperZaloPayZNSPreview>
    </Wrapper>
  );
};

export default ZNSPreview;
