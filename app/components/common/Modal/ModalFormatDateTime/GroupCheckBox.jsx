/* eslint-disable indent */
/* eslint-disable react/prop-types */
import React from 'react';
import styled from 'styled-components';
import { UIWrapperDisable as WrapperDisable } from '@xlab-team/ui-components';
import RadioGroup from '@material-ui/core/RadioGroup';
import Radio from '@material-ui/core/Radio';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import TRANSLATE_KEY from '../../../../messages/constant';
import { getTranslateMessage } from '../../../../containers/Translate/util';
import SelectTree from '../../../form/UISelectCondition';
import { WrapperFlex } from '../_UI/styled';
import { mapOptionsFormatTime, optionsFormatTime } from './constants';
import { StyledCheckbox } from './styled';

export const DivContainerSelectTree = styled.div`
  margin-top: 12px;

  .MuiFormGroup-root {
    ${({ isTypeModal }) =>
      isTypeModal ? 'flex-direction: column' : 'flex-direction: row'};
  }

  .private-checkbox {
    white-space: nowrap;
  }
`;
const DivContainerRadio = styled.div`
  margin-left: 28px;

  .MuiFormGroup-root {
    ${({ isTypeModal }) =>
      isTypeModal ? 'flex-direction: column' : 'flex-direction: row'};
  }
`;

const GroupCheckBox = props => {
  const onChangeFormatDate = e => {
    const { name } = e.target;
    props.onChangeRadioFormatDate(name);
  };

  const onChangeFormatTime = e => {
    const { name } = e.target;
    props.onChangeRadioFormatTime(name);
  };

  const onChangeFormatDateTime = item => {
    if (item.value === 'AM/PM') {
      props.onChangeCheckedAMPM(item.value);
    } else {
      props.onChangeChecked24Hour(item.value);
    }
  };

  const labelShortDate = getTranslateMessage(
    props.configFormat.format === 'MM/DD/YYYY'
      ? TRANSLATE_KEY._DATE_SHORT_FORMAT1
      : TRANSLATE_KEY._DATE_SHORT_FORMAT2,
    'Short - 04/15/2020 ',
  );

  const labelMediumDate = getTranslateMessage(
    props.configFormat.format === 'MM/DD/YYYY'
      ? TRANSLATE_KEY._DATE_MEDIUM_FORMAT1
      : TRANSLATE_KEY._DATE_MEDIUM_FORMAT2,
    'Medium - Apr 15, 2020',
  );

  const labelLongDate = getTranslateMessage(
    props.configFormat.format === 'MM/DD/YYYY'
      ? TRANSLATE_KEY._DATE_LONG_FORMAT1
      : TRANSLATE_KEY._DATE_LONG_FORMAT2,
    'Long - April 15, 2020',
  );

  const labelShortTime = getTranslateMessage(
    props.configFormat.time.timeFormat === '12'
      ? TRANSLATE_KEY._TIME_SHORT_FOTMAT1
      : TRANSLATE_KEY._TIME_SHORT_FOTMAT2,
    'Short - 6:23 PM',
  );

  const labelMediumTime = getTranslateMessage(
    props.configFormat.time.timeFormat === '12'
      ? TRANSLATE_KEY._TIME_MEDIUM_FORMAT1
      : TRANSLATE_KEY._TIME_MEDIUM_FORMAT2,
    'Short - 6:23 PM',
  );

  const labelLongTime = getTranslateMessage(
    props.configFormat.time.timeFormat === '12'
      ? TRANSLATE_KEY._TIME_LONG_FORMAT1
      : TRANSLATE_KEY._TIME_LONG_FORMAT2,
    'Long - 6:23:11 PM GMT +7',
  );

  return (
    <div>
      <DivContainerSelectTree className="private-form__control-wrapper">
        <WrapperFlex css="align-items: center;">
          <StyledCheckbox
            name="date-display-format"
            checked={props.checkedFormatDate}
            translateCode={TRANSLATE_KEY._}
            onClick={props.onChangeCheckedFormatDate}
          >
            {getTranslateMessage(TRANSLATE_KEY._, 'Display date as')}
          </StyledCheckbox>

          <SelectTree
            use="tree"
            options={props.dropdownFormatDate}
            value={props.mapDropdownFormatDate[props.configFormat.format]}
            onChange={props.onChangeTypeDateTime}
            selectWidth="150px"
          />
        </WrapperFlex>
        <DivContainerRadio isTypeModal={props.isTypeModal}>
          <WrapperDisable disabled={props.disableRadioFormatDate}>
            <RadioGroup
              name="RadiosCheckTypeConfig"
              value={props.nameItemCheckedFormatDate}
              onChange={onChangeFormatDate}
            >
              <FormControlLabel
                value="short"
                label={labelShortDate}
                control={
                  <Radio color="primary" size="small" name="shortDate" />
                }
              />

              <FormControlLabel
                value="medium"
                label={labelMediumDate}
                control={
                  <Radio color="primary" size="small" name="mediumDate" />
                }
              />

              <FormControlLabel
                value="long"
                control={<Radio color="primary" size="small" name="longDate" />}
                label={labelLongDate}
              />
            </RadioGroup>
          </WrapperDisable>
        </DivContainerRadio>
      </DivContainerSelectTree>
      <DivContainerSelectTree className="private-form__control-wrapper">
        <WrapperFlex css="align-items: center;">
          <StyledCheckbox
            name="date-display-format"
            checked={props.checkedFormatTime}
            translateCode={TRANSLATE_KEY._}
            onClick={props.onChangeCheckedFormatTime}
          >
            {getTranslateMessage(TRANSLATE_KEY._, 'Display time as')}
          </StyledCheckbox>

          <WrapperDisable disabled={props.disableRadioFormatTime}>
            <SelectTree
              use="tree"
              options={optionsFormatTime}
              value={mapOptionsFormatTime[props.nameCheckedFormatTime]}
              onChange={onChangeFormatDateTime}
              selectWidth="150px"
            />
          </WrapperDisable>
        </WrapperFlex>

        <DivContainerRadio isTypeModal={props.isTypeModal}>
          <WrapperDisable disabled={props.disableRadioFormatTime}>
            <RadioGroup
              name="RadiosCheckTypeConfig"
              value={props.nameItemCheckedFormatTime}
              onChange={onChangeFormatTime}
            >
              <FormControlLabel
                label={labelShortTime}
                value="short"
                control={
                  <Radio color="primary" size="small" name="shortTime" />
                }
              />

              <FormControlLabel
                label={labelMediumTime}
                value="medium"
                control={
                  <Radio color="primary" size="small" name="mediumTime" />
                }
              />

              <FormControlLabel
                value="long"
                label={labelLongTime}
                control={<Radio color="primary" size="small" name="longTime" />}
              />
            </RadioGroup>
          </WrapperDisable>
        </DivContainerRadio>
      </DivContainerSelectTree>
    </div>
  );
};

export default GroupCheckBox;
