import { getTranslateMessage } from '../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';
import { safeParse } from '../../../utils/common';

export const MAP_TITLE = {
  generalSettings: getTranslateMessage(
    TRANSLATE_KEY._INFO_STORY_SETTING,
    'Gerneral Settings',
  ),
  infoStorySetting: getTranslateMessage(
    TRANSLATE_KEY._INFO_STORY_SETTING_FREQ,
    'How many..',
  ),
  unlimited: getTranslateMessage(TRANSLATE_KEY._STORY_SETTING_NO_FREQ, ''),
  limited: getTranslateMessage(TRANSLATE_KEY._STORY_SETTING_LIMIT_FREQ, ''),
  infoTimePer: getTranslateMessage(TRANSLATE_KEY._INFO_TIME_PER, ''),
  infoDay: getTranslateMessage(TRANSLATE_KEY._OPT_TRIGGER_FC_TODAY, 'today'),
  infoWeek: getTranslateMessage(
    TRANSLATE_KEY._OPT_TRIGGER_FC_WEEK,
    'this week',
  ),
  infoMonth: getTranslateMessage(
    TRANSLATE_KEY._OPT_TRIGGER_FC_MONTH,
    'this month',
  ),
  infoYear: getTranslateMessage(
    TRANSLATE_KEY._OPT_TRIGGER_FC_YEAR,
    'this year',
  ),
  infoPerPerson: getTranslateMessage(TRANSLATE_KEY._INFO_PER_PERSON, ''),
  infoHourly: getTranslateMessage(
    TRANSLATE_KEY._OPT_TRIGGER_FC_HOUR,
    'this hourly',
  ),
  lifetime: getTranslateMessage(
    TRANSLATE_KEY._OPT_TRIGGER_FC_LIFETIME,
    'lifetime',
  ),
  infoEvery: getTranslateMessage(TRANSLATE_KEY._OPT_TRIGGER_FC_EVERY, 'every'),
  person: getTranslateMessage(TRANSLATE_KEY._, 'person'),
};
export const PERIOD_MAP_WEB_PERSONALIZE = {
  every: {
    value: 'every',
    name: 'every',
    label: MAP_TITLE.infoEvery,
  },
  hourly: {
    value: 'hourly',
    name: 'hourly',
    label: MAP_TITLE.infoHourly,
  },
  daily: {
    value: 'daily',
    name: 'daily',
    label: MAP_TITLE.infoDay,
  },
  weekly: {
    value: 'weekly',
    name: 'weekly',
    label: MAP_TITLE.infoWeek,
  },
  monthly: {
    value: 'monthly',
    name: 'monthly',
    label: MAP_TITLE.infoMonth,
  },
  yearly: {
    value: 'yearly',
    name: 'yearly',
    label: MAP_TITLE.infoYear,
  },
};
export const PERIOD_MAP = {
  hourly: {
    value: 'hourly',
    name: 'hourly',
    label: MAP_TITLE.infoHourly,
  },
  daily: {
    value: 'daily',
    name: 'daily',
    label: MAP_TITLE.infoDay,
  },
  weekly: {
    value: 'weekly',
    name: 'weekly',
    label: MAP_TITLE.infoWeek,
  },
  monthly: {
    value: 'monthly',
    name: 'monthly',
    label: MAP_TITLE.infoMonth,
  },
  yearly: {
    value: 'yearly',
    name: 'yearly',
    label: MAP_TITLE.infoYear,
  },
};
export const MAP_PERSON = {
  person: {
    value: 'person',
    name: 'person',
    label: MAP_TITLE.person,
  },
  ip: {
    value: 'ip',
    name: 'ip',
    label: 'IP',
  },
};
export const PERIOD_MAP_LIFETIME = {
  every: {
    value: 'every',
    name: 'every',
    label: MAP_TITLE.infoEvery,
  },
  hourly: {
    value: 'hourly',
    name: 'hourly',
    label: MAP_TITLE.infoHourly,
  },
  daily: {
    value: 'daily',
    name: 'daily',
    label: MAP_TITLE.infoDay,
  },
  weekly: {
    value: 'weekly',
    name: 'weekly',
    label: MAP_TITLE.infoWeek,
  },
  monthly: {
    value: 'monthly',
    name: 'monthly',
    label: MAP_TITLE.infoMonth,
  },
  yearly: {
    value: 'yearly',
    name: 'yearly',
    label: MAP_TITLE.infoYear,
  },
  lifetime: {
    value: 'lifetime',
    name: 'lifetime',
    label: MAP_TITLE.lifetime,
  },
};
export const EVENT_MAP = {
  impression: {
    value: 'impression',
    name: 'impression',
    label: 'Impression',
  },
  click: {
    value: 'click',
    name: 'click',
    label: 'Click',
    // disabled: true,
  },
  submit_optin: {
    value: 'submit_optin',
    name: 'Submit Optin',
    label: 'Submit Optin',
  },
};
export const EVENT_LIST = Object.keys(EVENT_MAP).map(each => EVENT_MAP[each]);

export const PERIOD_LIST = Object.keys(PERIOD_MAP).map(
  each => PERIOD_MAP[each],
);
export const PERIOD_LIST_WEB_PERSONALIZE = Object.keys(
  PERIOD_MAP_WEB_PERSONALIZE,
).map(each => PERIOD_MAP_WEB_PERSONALIZE[each]);

export const PERIOD_LIST_LIFETIME = Object.keys(PERIOD_MAP_LIFETIME).map(
  each => PERIOD_MAP_LIFETIME[each],
);

export const LIST_PERSON = Object.keys(MAP_PERSON).map(
  each => MAP_PERSON[each],
);
// refactor
export const toAPIFrequencyCapping = (data, channelActive) => {
  const {
    cappingSel = 'unlimited',
    timeUnit = PERIOD_MAP.daily,
    value = 1,
    event = EVENT_MAP.impression,
    timeValue = 1,
    object = MAP_PERSON.object,
    isRecordEvent = false,
  } = data;

  if (cappingSel === 'unlimited') return null;

  const isWebPersonalize =
    channelActive && (channelActive.value === 2 || channelActive === 2);
  const isEvery = timeUnit?.value === 'every';

  const base = {
    timeUnit: timeUnit.value,
    value,
    object: object.value,
    isRecordEvent,
  };

  if (isWebPersonalize) {
    return isEvery
      ? { ...base, event: event.value, timeValue, unitValue: 'day' }
      : { ...base, event: event.value };
  }

  if (isEvery) {
    return { ...base, timeValue, unitValue: 'day' };
  }

  return base;
};

// export const toAPIFrequencyCapping = (data, channelActive) => {
//   const {
//     cappingSel = 'unlimited',
//     timeUnit = PERIOD_MAP.daily,
//     value = 1,
//     event = EVENT_MAP.impression,
//     timeValue = 1,
//     object = MAP_PERSON.object,
//     isRecordEvent = false,
//   } = data;
//   if (cappingSel === 'unlimited') {
//     return null;
//   }
//   if (channelActive && (channelActive.value === 2 || channelActive === 2)) {
//     if (timeUnit.value === 'every') {
//       return {
//         timeUnit: timeUnit.value,
//         value,
//         event: event.value,
//         object: object.value,
//         isRecordEvent,
//         timeValue,
//       };
//     }
//     return {
//       timeUnit: timeUnit.value,
//       value,
//       event: event.value,
//       object: object.value,
//       isRecordEvent,
//     };
//   }
//   if (
//     channelActive &&
//     channelActive.value !== 2 &&
//     timeUnit.value === 'every'
//   ) {
//     return {
//       timeUnit: timeUnit.value,
//       timeValue,
//       value,
//       unitValue: 'day',
//       object: object.value,
//       isRecordEvent,
//     };
//   }
//   return {
//     timeUnit: timeUnit.value,
//     value,
//     object: object.value,
//     isRecordEvent,
//   };
// };
export const toAPIFrequencyCappingV2 = (data, dataV1) => {
  if (!dataV1) return null;
  const dataOut =
    data?.map(item => {
      const base = {
        timeUnit: item.timeUnit.value,
        value: item.value,
        event: item.event.value,
        object: item.object.value,
        isRecordEvent: item.isRecordEvent,
      };
      if (item.timeUnit.value === 'every') {
        return { ...base, timeValue: item.timeValue };
      }
      return base;
    }) || [];

  return [dataV1, ...dataOut];
};

export const toUIFrequencyCapping = data => {
  const dataFrequencyCapping = safeParse(data, {});
  // console.log('dataFrequencyCapping===>', dataFrequencyCapping);
  if (Object.keys(dataFrequencyCapping).length === 0) {
    return {
      cappingSel: 'unlimited',
      value: 1,
      timeUnit: PERIOD_MAP.daily,
      event: EVENT_MAP.impression,
      timeValue: 1,
      object: MAP_PERSON.person,
    };
  }

  const {
    timeUnit,
    value,
    event,
    object,
    timeValue = 1,
    isRecordEvent,
  } = dataFrequencyCapping;

  return {
    cappingSel: 'limited',
    value,
    timeUnit: PERIOD_MAP_LIFETIME[timeUnit] || PERIOD_MAP_LIFETIME.daily,
    event: EVENT_MAP[event] || EVENT_MAP.impression,
    timeValue,
    object: MAP_PERSON[object] || MAP_PERSON.person,
    isRecordEvent,
  };
};
export const toUIFrequencyCappingV2 = data => {
  const dataFrequencyCapping = safeParse(data, []);
  // console.log('dataFrequencyCapping===>', dataFrequencyCapping);
  if (dataFrequencyCapping.length === 0) {
    return null;
  }
  const dataOut = dataFrequencyCapping
    .filter((_, index) => index > 0)
    .map(item => {
      return {
        cappingSel: 'limited',
        value: item.value,
        timeUnit:
          PERIOD_MAP_LIFETIME[item.timeUnit] || PERIOD_MAP_LIFETIME.daily,
        event: EVENT_MAP[item.event] || EVENT_MAP.impression,
        timeValue: item.timeValue || 1,
        object: MAP_PERSON[item.object] || MAP_PERSON.person,
        isRecordEvent: item.isRecordEvent,
      };
    });

  return dataOut;
};
export const optionFrequencyV2 = (option, value, stateV2, state = null) => {
  // Tạo một Set chứa các id cần xoá, nhưng giữ lại id đang được chọn = value
  const removeIdsSet = new Set(
    stateV2.map(item => item.event.value).filter(each => each !== value),
  );
  // Lọc ra các phần tử không có trong danh sách xoá
  const filteredOptions = option.filter(item =>
    state
      ? !removeIdsSet.has(item.value) && item.value !== state.event.value
      : !removeIdsSet.has(item.value),
  );
  return filteredOptions;
};
