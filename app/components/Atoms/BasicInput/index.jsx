// Libraries
import React, { forwardRef, memo, useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import { debounce, isArray, partialRight } from 'lodash';

// Components
import { Flex, Input, Typography } from '@antscorp/antsomi-ui';

// Constants
import { globalToken } from '@antscorp/antsomi-ui/es/constants';

const BasicInput = forwardRef(
  (
    {
      id,
      name,
      value,
      errors,
      style,
      disabled,
      maxLength,
      placeholder,
      onChange,
    },
    ref,
  ) => {
    const isErr = useMemo(() => isArray(errors) && errors.length > 0, [errors]);

    const onChangeDebounce = useCallback(
      debounce((event, callback) => {
        if (event.target) {
          callback(event.target.value);
        }
      }, 350),
      [],
    );

    const onChangeOut = useCallback(partialRight(onChangeDebounce, onChange), [
      onChange,
      onChangeDebounce,
    ]);

    return (
      <Flex vertical gap={5}>
        <Input
          ref={ref}
          id={id}
          name={name}
          style={style}
          disabled={disabled}
          maxLength={maxLength}
          status={isErr ? 'error' : undefined}
          placeholder={placeholder}
          value={value}
          onChange={onChangeOut}
        />

        {isErr
          ? errors.map(err => (
              <Typography.Text
                key={err}
                style={{ color: globalToken?.colorError }}
              >
                {err}
              </Typography.Text>
            ))
          : null}
      </Flex>
    );
  },
);

BasicInput.propTypes = {
  id: PropTypes.string,
  name: PropTypes.string,
  value: PropTypes.string,
  errors: PropTypes.array,
  style: PropTypes.object,
  disabled: PropTypes.bool,
  placeholder: PropTypes.string,
  maxLength: PropTypes.number,
  onChange: PropTypes.func,
};

export default memo(BasicInput);
