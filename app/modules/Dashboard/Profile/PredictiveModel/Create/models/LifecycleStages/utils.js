/* eslint-disable no-param-reassign */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-restricted-syntax */
/* eslint-disable camelcase */
/* eslint-disable indent */
/* eslint-disable arrow-body-style */
/* eslint-disable no-multi-assign */
import produce from 'immer';
import {
  difference,
  get,
  groupBy,
  isEmpty,
  isNumber,
  omit,
  pick,
  sortBy,
  uniq,
} from 'lodash';
import { OrderedMap } from 'immutable';
import {
  getObjectPropSafely,
  toLowerCaseNonAccentVietnamese,
} from '../../../../../../../utils/web/utils';
import { MODELS } from '../../../config';
import {
  SEGMENTS,
  DEFAULT_EVENTS,
  TRAINING_STEPS,
  TRAINING_STEP_KEYS,
  SEGMENT_KEYS,
  TRANSACTION_STAGES,
  TRANSACTION_STAGE_KEYS,
  PREPARE_DATA_KEYS,
  TRAINING_CONTENTS_BY_STEP,
  SEGMENT_GROUPS,
  SEGMENT_GROUP_KEYS,
  METRICS,
  METRIC_KEYS,
  SEGMENT_GROUP_RATIO,
  BARCHART_TOOLTIP,
} from './constant';
import {
  toAPIPerformEvent,
  toUIPerformEvent,
} from 'components/common/UIPerformEventV2/utils';
import { toRefinePropertiesAPI } from 'components/common/UIPerformEventV2/BlockConditionPerformEvent/Refine/utils';
import { addMessageToQueue } from 'utils/web/queue';
import {
  fillDataBarchart,
  refinePropertiesAPIToCondtionsAPI,
  serializeBasicBarchart,
  toAPIDataUpdate,
} from '../../utils';

const PATH =
  'app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/utils.js';

export const predicateInitDefaultEvents = () => {
  const listMatched = [];

  return event => {
    const index = DEFAULT_EVENTS.findIndex(
      eventLabel =>
        toLowerCaseNonAccentVietnamese(event.label).includes(eventLabel) &&
        !listMatched.includes(eventLabel),
    );

    if (index !== -1) {
      listMatched.push(DEFAULT_EVENTS[index]);
      return true;
    }

    return false;
  };
};

export const initSegments = () => {
  const entries = Object.entries(SEGMENTS).map(([segmentKey, segment]) => {
    const otherProperties = {
      customers: 0,
    };

    const noneRange = [
      SEGMENT_KEYS.FirstTimeBuyer,
      SEGMENT_KEYS.InstantFirstTimeBuyer,
    ].includes(segmentKey);

    if (!noneRange) {
      otherProperties.from = 0;
      otherProperties.to = 0;
    }

    return [
      segmentKey,
      {
        ...segment,
        ...otherProperties,
      },
    ];
  });

  return {
    byKeys: Object.fromEntries(entries),
    allKeys: [
      SEGMENT_KEYS.ActiveProspect,
      SEGMENT_KEYS.ColdProspect,
      SEGMENT_KEYS.InactiveProspect,
      SEGMENT_KEYS.LapsedBuyer,
      SEGMENT_KEYS.FirstTimeBuyer,
      SEGMENT_KEYS.InstantFirstTimeBuyer,
      SEGMENT_KEYS.RepeatBuyer,
      SEGMENT_KEYS.RegainedBuyer,
    ],
  };
};

export const initTrainings = () => {
  const entries = Object.entries(TRAINING_STEPS).map(([stepKey, step]) => [
    stepKey,
    initTraningStep(step),
  ]);

  return {
    byKeys: Object.fromEntries(entries),
    allKeys: [
      TRAINING_STEP_KEYS.Engagement,
      TRAINING_STEP_KEYS.Recency,
      TRAINING_STEP_KEYS.TransactionOrder,
      TRAINING_STEP_KEYS.RetentionDuration,
      TRAINING_STEP_KEYS.CustomerStage,
    ],
  };
};

export const initTraningStep = step => {
  const result = {
    ...step,
    contents: initTraningContents(step.key),
    hidden: false,
    aggregation: [],
  };

  if (step.metrics) {
    result.metrics = step.metrics.map(metricKey => ({
      ...METRICS[metricKey],
      show: true,
    }));
  }

  return result;
};

export const initTraningContents = trainingStepKey => {
  const entries = TRAINING_CONTENTS_BY_STEP[trainingStepKey].map(contentKey => [
    contentKey,
    {
      trained: false,
      isTraining: false,
      needToReTraining: false,
    },
  ]);

  return Object.fromEntries(entries);
};

export const initTransactionStates = () => {
  const entries = Object.entries(TRANSACTION_STAGES).map(([key, stage]) => [
    key,
    { ...stage },
  ]);

  return {
    data: [],
    byKeys: Object.fromEntries(entries),
    allKeys: [
      TRANSACTION_STAGE_KEYS.FirstPurchase,
      TRANSACTION_STAGE_KEYS.InstantFirstPurchase,
      TRANSACTION_STAGE_KEYS.Repeat,
      TRANSACTION_STAGE_KEYS.Regained,
    ],
    vennDiagramKey: undefined,
  };
};

export const getSegmentsToCurrentStep = ({
  trainingAllKeys,
  actTrainingIndex,
  segmentByKeys,
}) => {
  const segments = [...trainingAllKeys]
    .splice(0, actTrainingIndex + 1)
    .reduce((acc, trainingKey) => {
      const segmentsByTraining = Object.values(segmentByKeys).filter(
        segment => segment.trainingKey === trainingKey,
      );

      acc.push(...sortBy(segmentsByTraining, 'id'));

      return acc;
    }, []);

  return segments;
};

export const segmentsToScore = (segments, trainingKey) => {
  const totalCustomers = segments.reduce(
    (acc, segment) => {
      acc[segment.groupKey] += isNumber(segment.customers)
        ? segment.customers
        : 0;

      return acc;
    },
    {
      [SEGMENT_GROUP_KEYS.Buyer]: 0,
      [SEGMENT_GROUP_KEYS.NonBuyer]: 0,
    },
  );

  return segments.map(segment =>
    segmentToScore({
      segment,
      trainingKey,
      total: totalCustomers[segment.groupKey] || 0,
    }),
  );
};

export const segmentToScore = ({ segment, trainingKey, total }) => ({
  key: segment.key,
  showIndex: true,
  showIndicator: true,
  label: segment.label,
  subLabel: getSegmentSubLabel(segment, trainingKey),
  percent: getSegmentPercent(segment, total),
  tip: segment.tip,
  value: segment.customers,
  color: segment.color,
});

const getSegmentPercent = (segment, total) => {
  if (total === 0) return '0.00 %';

  return `${((segment.customers / total) * 100).toFixed(2)} %`;
};

const getSegmentSubLabel = (segment, trainingKey) => {
  const { key, from, to } = segment;

  let subLabel = '';

  switch (trainingKey) {
    case TRAINING_STEP_KEYS.Engagement:
      if (key === SEGMENT_KEYS.ActiveProspect) {
        subLabel = `≤ ${to} days`;
      } else if (key === SEGMENT_KEYS.InactiveProspect) {
        subLabel = `> ${from} days`;
      } else {
        subLabel = `${from} - ${to} days`;
      }
      break;
    case TRAINING_STEP_KEYS.Recency:
      if (key === SEGMENT_KEYS.LapsedBuyer) {
        subLabel = `≥ ${from} days`;
      }
      break;
    case TRAINING_STEP_KEYS.TransactionOrder:
      if (key === SEGMENT_KEYS.FirstTimeBuyer) {
        // subLabel = `<= ${to} days`;
      } else if (key === SEGMENT_KEYS.FirstTimeBuyer) {
        // subLabel = `> ${from} days`;
      }
      break;
    case TRAINING_STEP_KEYS.RetentionDuration:
      if (key === SEGMENT_KEYS.RepeatBuyer) {
        subLabel = `≤ ${to} days`;
      } else if (key === SEGMENT_KEYS.RegainedBuyer) {
        subLabel = `> ${from} days`;
      }
      break;
    case TRAINING_STEP_KEYS.CustomerStage:
      break;
    default:
  }

  return subLabel;
};

export const transactionStageToScore = transactionStage => ({
  showIndicator: true,
  showIndex: true,
  label: transactionStage.label,
  key: transactionStage.key,
  color: transactionStage.color,
  value: transactionStage.customers,
});

const toAPIEvents = events =>
  toAPIPerformEvent(events, {
    getCustomProperty: item => {
      const eventTrackingName = getObjectPropSafely(
        () => item.get('property').eventTrackingCode,
        null,
      );

      return {
        hasFilter: item.get('hasFilter'),
        ...(eventTrackingName && { eventTrackingName }),
      };
    },
  });

export const transactionStagesToScores = ({
  transactionByKeys,
  transactionAllKeys,
  transactionData,
}) =>
  transactionAllKeys
    .map(transactionKey => {
      const dataByKey = transactionData.find(
        data => data.sets.length === 1 && data.sets.at(0) === transactionKey,
      );

      return {
        ...transactionByKeys[transactionKey],
        customers: get(dataByKey, 'customers', 0),
      };
    })
    .map(transactionStageToScore);

const toModelConfig = trainModel => {
  const { segments, trainings } = trainModel;

  const inputRangeSteps = [
    TRAINING_STEP_KEYS.Engagement,
    TRAINING_STEP_KEYS.Recency,
    TRAINING_STEP_KEYS.RetentionDuration,
  ];
  const granularitySteps = [
    TRAINING_STEP_KEYS.Recency,
    TRAINING_STEP_KEYS.RetentionDuration,
  ];

  const inputRanges = inputRangeSteps.reduce((acc, trainingKey) => {
    const key = `${trainingKey}_input_ranges`;

    acc[key] = {};

    const segmentsByTrainingKey = Object.values(segments.byKeys).filter(
      segment => segment.trainingKey === trainingKey,
    );

    for (const segment of segmentsByTrainingKey) {
      acc[key][segment.id] = {
        min: String(segment.from),
        max: String(segment.to),
      };
    }

    return acc;
  }, {});

  const granularities = granularitySteps.reduce((acc, trainingKey) => {
    const key = `${trainingKey}_granularity`;

    acc[key] = trainings.byKeys[trainingKey].granularity.value;

    return acc;
  }, {});

  return {
    ...inputRanges,
    ...granularities,
  };
};

export const toAPIData = settings => {
  const { prepareData, trainModel, applyModel } = settings;

  const {
    computeSchedule,
    notificationSetup,
    dataUpdate,
    triggerEvent,
  } = applyModel;

  const data = {
    modelType: +MODELS.LIFECYCLE_STAGE,

    dataCondition: {
      ...pick(prepareData, [
        PREPARE_DATA_KEYS.transaction,
        PREPARE_DATA_KEYS.transactionConfigs,
        PREPARE_DATA_KEYS.customerRegister,
        PREPARE_DATA_KEYS.eventTimeRange,
      ]),
      transactionFilters: toRefinePropertiesAPI(
        prepareData[PREPARE_DATA_KEYS.transactionRefine],
      ),
      events: toAPIEvents(prepareData[PREPARE_DATA_KEYS.events]),
    },

    modelConfig: toModelConfig(trainModel),

    alertSetting: notificationSetup,
    computeSchedule,
    dataUpdate: toAPIDataUpdate(dataUpdate),

    properties: {
      trainModel,
      applyModel: {
        triggerEvent,
      },
    },
  };

  return data;
};

export const getTrainingStepsByContents = contentKeys => {
  const entries = Object.entries(TRAINING_STEPS).filter(
    ([trainingStepKey]) =>
      difference(contentKeys, TRAINING_CONTENTS_BY_STEP[trainingStepKey] || [])
        .length === 0,
  );

  return Object.fromEntries(entries);
};

export const initSegmentGroups = () => {
  const entries = Object.entries(SEGMENT_GROUPS).map(([segmentKey, group]) => [
    segmentKey,
    {
      ...group,
      customers: 0,
    },
  ]);

  return {
    byKeys: Object.fromEntries(entries),
    allKeys: [SEGMENT_GROUP_KEYS.Buyer, SEGMENT_GROUP_KEYS.NonBuyer],
  };
};

export const toVennDiagram = ({
  dataSets = [],
  byKeys: transactionByKeys = {},
}) => {
  const results = dataSets.map(data => {
    const otherProperties = {
      value: data.customers,
    };

    if (data.sets.length === 1) {
      otherProperties.name = transactionByKeys[data.sets.at(0)].label;
      otherProperties.color = transactionByKeys[data.sets.at(0)].color;
    } else {
      // Case First Purchase Stage + other stage
      if (data.sets.length === 2) {
        const key = data.sets.find(
          transactionKey =>
            transactionKey !== TRANSACTION_STAGE_KEYS.FirstPurchase,
        );

        otherProperties.color = TRANSACTION_STAGES[key].color;
      }

      otherProperties.name = data.sets
        .map(transactionKey => transactionByKeys[transactionKey].label)
        .join(' & ');
    }

    return {
      ...omit(data, ['customers']),
      ...otherProperties,
    };
  });

  return results;
};

export const getRangesFromSegments = segments => {
  const results = segments.reduce((acc, segment) => {
    acc.push(segment.from, segment.to);

    return acc;
  }, []);

  return uniq(results).sort((a, b) => a - b);
};

export const getBarchartSideLabel = trainingKey => {
  const sideLabel = {};

  if (
    TRAINING_STEP_KEYS.Engagement === trainingKey ||
    TRAINING_STEP_KEYS.Recency === trainingKey
  ) {
    sideLabel.left = 'Num of Customers';
  }

  if (TRAINING_STEP_KEYS.TransactionOrder === trainingKey) {
    sideLabel.left = 'Num of Customers / Num of Transactions';
    sideLabel.right = 'Revenue / Cumulative Revenue';
  }

  if (TRAINING_STEP_KEYS.RetentionDuration === trainingKey) {
    sideLabel.left = 'Num of Customers / Num of Transactions';
    sideLabel.right = 'Revenue';
  }

  return sideLabel;
};

const getGroupBarchartSeries = ({
  trainingKey,
  aggregation,
  metricOrdered,
}) => {
  const series = [];

  const { metrics } = TRAINING_STEPS[trainingKey];

  if (isEmpty(metrics)) return series;

  metrics.forEach(metricKey => {
    let tempSeries = {
      type: 'bar',
      data: aggregation.map(row => ({
        value: row.data[metricOrdered.indexOf(metricKey)],
        min: row.min,
        max: row.max,
      })),
    };

    if (metricKey === METRIC_KEYS.cumulativeRevenue) {
      let cumulate = 0;

      tempSeries = {
        type: 'line',
        smooth: 0.3,
        symbol: 'none',
        data: aggregation.map(row => {
          cumulate += row.data[metricOrdered.indexOf(METRIC_KEYS.revenue)];

          return {
            value: cumulate,
            min: row.min,
            max: row.max,
          };
        }),
      };
    }

    const isMetricRevenue = [
      METRIC_KEYS.revenue,
      METRIC_KEYS.cumulativeRevenue,
    ].includes(metricKey);

    series.push({
      ...tempSeries,
      name: METRICS[metricKey].label,
      yAxisIndex: isMetricRevenue ? 1 : 0,
    });
  });

  return series;
};

export const toBasicBarchart = ({
  segments,
  aggregation,
  trainingKey,
  zoombar,
}) => {
  const range = getRangesFromSegments(segments);
  const { default: zoombarDefault, range: zoombarRange } = zoombar;
  const [min, max] = zoombarDefault;

  const data = {
    data: aggregation,
    range,
    min,
    max,
    zoombar: {
      range: zoombarRange,
    },
  };

  return {
    config: {
      title: {
        text: TRAINING_STEPS[trainingKey].label,
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        ...(BARCHART_TOOLTIP[trainingKey] && {
          formatter: BARCHART_TOOLTIP[trainingKey],
        }),
      },
    },
    sideLabel: getBarchartSideLabel(trainingKey),
    colors: segments.map(segment => segment.color),
    data,
  };
};

export const toBarchart = ({
  aggregation,
  metrics,
  segments,
  trainingKey,
  zoombar,
}) => {
  const basicBarchart = [
    TRAINING_STEP_KEYS.Engagement,
    TRAINING_STEP_KEYS.Recency,
  ].includes(trainingKey);

  const groupBarchart = [
    TRAINING_STEP_KEYS.TransactionOrder,
    TRAINING_STEP_KEYS.RetentionDuration,
  ].includes(trainingKey);

  if (basicBarchart) {
    return toBasicBarchart({
      segments,
      aggregation,
      trainingKey,
      zoombar,
    });
  }

  if (groupBarchart) {
    return toGroupBarchart({
      segments,
      aggregation,
      metrics,
      trainingKey,
      zoombar,
    });
  }

  return {};
};

export const toGroupBarchart = ({
  segments,
  metrics,
  aggregation,
  trainingKey,
  zoombar,
}) => {
  const title = {
    text: TRAINING_STEPS[trainingKey].label,
  };

  const metricOrdered = [
    METRIC_KEYS.numOfCustomers,
    METRIC_KEYS.numOfTransactions,
    METRIC_KEYS.revenue,
    METRIC_KEYS.cumulativeRevenue,
  ];

  const metricLabels = metricOrdered.map(metricKey => METRICS[metricKey].label);

  const xAxis = [
    {
      axisTick: {
        alignWithLabel: true,
      },
      show: true,
      type: 'category',
      data: aggregation.map(data => ({
        value:
          // trainingKey === TRAINING_STEP_KEYS.TransactionOrder
          //   ? `${index + 1}${nthNumber(index + 1)}` :
          Math.round((data.min + data.max) / 2),
        textStyle: {
          fontSize: 11,
        },
      })),
    },
  ];

  const range = getRangesFromSegments(segments);
  const { default: zoombarDefault, range: zoombarRange } = zoombar;
  const [min, max] = zoombarDefault;

  // let min;
  // let max;

  // if (trainingKey === TRAINING_STEP_KEYS.TransactionOrder) {
  //   min = 0;
  //   max = aggregation.length;
  // } else {
  //   min = range.at(0);
  //   max = range.at(-1);
  // }

  return {
    data: {
      data: undefined,
      min,
      max,
      zoombar: {
        range: zoombarRange,
      },
      ...(trainingKey !== TRAINING_STEP_KEYS.TransactionOrder && {
        range,
      }),
    },
    sideLabel: getBarchartSideLabel(trainingKey),
    config: {
      legend: {
        show: false,
        data: metricLabels,
        selected: Object.fromEntries(
          metrics.map(metric => [metric.label, metric.show]),
        ),
      },
      color: metricOrdered.map(metricKey => METRICS[metricKey].color),
      title,
      series: getGroupBarchartSeries({
        trainingKey,
        aggregation,
        metricOrdered,
      }),
      xAxis,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        ...(BARCHART_TOOLTIP[trainingKey] && {
          formatter: BARCHART_TOOLTIP[trainingKey],
        }),
      },
    },
  };
};

export const toPiechart = ({ trainingKey, segments }) => {
  const customerStage = Object.values(segments).reduce((acc, segment) => {
    acc[segment.id] = { customers: segment.customers };
    return acc;
  }, {});

  return [
    getTrainingPieChart({
      trainingKey,
      segmentGroupKey: SEGMENT_GROUP_KEYS.NonBuyer,
      customerStage,
    }),
    getTrainingPieChart({
      trainingKey,
      segmentGroupKey: SEGMENT_GROUP_KEYS.Buyer,
      customerStage,
    }),
  ];
};

const getTrainingPieChart = ({
  trainingKey,
  segmentGroupKey,
  customerStage,
}) => {
  return {
    groupName: SEGMENT_GROUPS[segmentGroupKey].label,
    ratio: SEGMENT_GROUP_RATIO[segmentGroupKey],
    data: Object.values(SEGMENTS)
      .filter(segment => segment.groupKey === segmentGroupKey)
      .map(segment => {
        return {
          color: segment.color,
          name: segment.label,
          selected: segmentGroupKey === SEGMENT_GROUP_KEYS.NonBuyer,
          status: getSegmentStatus(segment, trainingKey),
          value: customerStage[SEGMENTS[segment.key].id].customers || 0,
        };
      }),
  };
};

const getSegmentStatus = (segment, trainingKey) => {
  if (trainingKey === TRAINING_STEP_KEYS.CustomerStage) {
    return 'active';
  }

  const stepInfo = TRAINING_STEPS[trainingKey];

  const segmentGroup = groupBy(SEGMENTS, 'trainingKey');

  const isShow = Object.values(TRAINING_STEPS)
    .filter(trainingStep => trainingStep.id <= stepInfo.id)
    .some(trainingStep =>
      segmentGroup[trainingStep.key].some(
        segmentInfo => segmentInfo.key === segment.key,
      ),
    );

  const isActive = segment.trainingKey === trainingKey;

  return isShow ? (isActive ? 'active' : 'inactive') : 'disabled';
};

const toAPIEngagementEvent = events => {
  const eventConfig = [];

  events.forEach(event => {
    const eventInfo = getObjectPropSafely(() => event.first(), null);
    const eventName = getObjectPropSafely(
      () => eventInfo.get('property').eventTrackingCode,
      null,
    );
    const insightPropertyIds = getObjectPropSafely(
      () => eventInfo.get('dataSources'),
      null,
    );

    if (eventName && insightPropertyIds) {
      eventConfig.push({
        event_name: eventName,
        insight_property_ids: insightPropertyIds,
      });
    }
  });

  return eventConfig;
};

const toAPITransactionTimeRange = timeRange => {
  let isAllTimeTransaction;
  let startDateTransaction;
  let endDateTransaction;

  if (timeRange === 'all_time') {
    isAllTimeTransaction = true;
    startDateTransaction = undefined;
    endDateTransaction = undefined;
  } else if (!isEmpty(timeRange)) {
    const { startDate, endDate } = timeRange;
    isAllTimeTransaction = false;
    startDateTransaction = startDate.date;
    endDateTransaction = endDate.date;
  }

  return {
    isAllTimeTransaction,
    startDateTransaction,
    endDateTransaction,
  };
};

const toAPITransactionConditions = conditions => {
  const defaultRules = {
    OR: [
      {
        AND: [],
      },
    ],
  };

  return {
    rules: getObjectPropSafely(
      () =>
        refinePropertiesAPIToCondtionsAPI(
          toRefinePropertiesAPI(conditions),
          'bo',
        ),
      defaultRules,
    ),
  };
};

export function toAPIPrepareData(prepareData) {
  const {
    transaction,
    transactionRefine,
    transactionConfigs,
    customerRegister,
    events,
    eventTimeRange,
  } = prepareData;

  const {
    isAllTimeTransaction,
    startDateTransaction,
    endDateTransaction,
  } = toAPITransactionTimeRange(get(transactionConfigs, 'timeRange'));

  return {
    transaction_source: get(transaction, 'itemTypeName'),
    item_type_id: get(transaction, 'itemTypeId'),
    is_all_time_transaction: isAllTimeTransaction,
    start_date_transaction: startDateTransaction,
    end_date_transaction: endDateTransaction,
    conditions: toAPITransactionConditions(transactionRefine),
    customer_id: get(transactionConfigs, 'customerIdentity.value'),
    date: get(transactionConfigs, 'transactionDate.value'),
    order: get(transactionConfigs, 'uniqueTransaction.value'),
    revenue: get(transactionConfigs, 'revenue.value'),
    customer_created_date: get(customerRegister, 'value'),
    event_config: toAPIEngagementEvent(events),
    start_date_event: get(eventTimeRange, 'startDate.date'),
    end_date_event: get(eventTimeRange, 'endDate.date'),
  };
}

export const mapRefineUI = data => {
  const dataInit = OrderedMap({
    'data-init': OrderedMap({
      backup: data,
      isInit: true,
    }),
  });

  return dataInit;
};

export function serializeDataConditionAPI(dataCondition) {
  try {
    const {
      transaction,
      transactionFilters,
      transactionConfigs,
      customerRegister,
      events,
      eventTimeRange,
    } = dataCondition;

    const prepareData = {
      transaction,
      transactionRefine: mapRefineUI(transactionFilters),
      transactionConfigs,
      customerRegister,
      events: toUIPerformEvent(events),
      eventTimeRange,
    };

    return prepareData;
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: serializeDataConditionAPI.name,
      data: err.stack,
    });
    return {};
  }
}

export const adjustRangesData = data => {
  try {
    const result = Object.entries(data).reduce((acc, [segmentKey, range]) => {
      acc[segmentKey] = range;

      const prevKey = +segmentKey - 1;
      const prevSegment = data[prevKey];

      // fix minValue to maxValue of prev segment
      if (prevSegment && prevSegment.max) {
        acc[segmentKey] = {
          ...range,
          min: prevSegment.max,
        };
      }

      return acc;
    }, {});

    return result;
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: adjustRangesData.name,
      data: err.stack,
    });
    return {};
  }
};

export const filterAggregationAPI = data => {
  return Object.entries(data).reduce((acc, [key, val]) => {
    if (val.min !== val.max) {
      acc[key] = val;
    }

    return acc;
  }, {});
};

export const getDefaultRangesAPI = trainingKey => {
  return Object.values(SEGMENTS).reduce((acc, segment) => {
    if (segment.trainingKey === trainingKey) {
      const segmentData = { customers: 0, min: 0, max: 0 };

      if (trainingKey !== TRAINING_STEP_KEYS.TransactionOrder) {
        segmentData.min = 0;
        segmentData.max = 0;
      }

      acc[segment.id] = segmentData;
    }

    return acc;
  }, {});
};

const serializeGroupBarchart = (data, zoombarRangeDefault) => {
  const [min = 0, max = 0] = zoombarRangeDefault;

  const metricOrdered = [
    METRIC_KEYS.numOfCustomers,
    METRIC_KEYS.numOfTransactions,
    METRIC_KEYS.revenue,
  ];

  const filledData = fillDataBarchart({
    data: Object.values(data).map(item => ({
      min: item.min,
      max: item.max,
      data: metricOrdered.map(metric => item[metric]),
    })),
    min,
    max,
    result: { data: new Array(metricOrdered.length).fill(0) },
  });

  return filledData;
};

export const serializeAggregationAPI = ({
  data,
  trainingKey,
  zoombarRangeDefault,
}) => {
  try {
    let result;

    switch (trainingKey) {
      case TRAINING_STEP_KEYS.Engagement:
      case TRAINING_STEP_KEYS.Recency: {
        result = serializeBasicBarchart(data, zoombarRangeDefault);
        break;
      }
      case TRAINING_STEP_KEYS.TransactionOrder:
      case TRAINING_STEP_KEYS.RetentionDuration: {
        result = serializeGroupBarchart(data, zoombarRangeDefault);
        break;
      }
      default:
        result = [];
    }

    return result;
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: serializeAggregationAPI.name,
      data: err.stack,
    });
    return [];
  }
};

export const serializeRangesAPI = (data, trainingKey, inputRanges) => {
  try {
    const segmentTrainingKey = groupBy(SEGMENTS, 'trainingKey')[trainingKey];

    const adjustedData = adjustRangesData(data);

    const result = segmentTrainingKey.reduce((acc, segment) => {
      const { id: segmentId, key: segmentKey } = segment;

      let segmentData = adjustedData[segmentId] || { customers: 0 };

      if (!isEmpty(inputRanges)) {
        segmentData = { ...inputRanges[segmentId], ...segmentData };
      }

      acc[segmentKey] = segmentData;

      return acc;
    }, {});

    return result;
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: serializeRangesAPI.name,
      data: err.stack,
    });
    return {};
  }
};

export const serializeTransactionStagesAPI = data => {
  try {
    const firstPurchaseStage = data.find(
      item =>
        item.sets.length === 1 &&
        item.sets[0] === TRANSACTION_STAGE_KEYS.FirstPurchase,
    );
    const otherStages = data.filter(
      item =>
        item.sets.length === 1 &&
        item.sets[0] !== TRANSACTION_STAGE_KEYS.FirstPurchase,
    );

    const additionalStages = otherStages.map(stage => ({
      sets: [firstPurchaseStage.sets[0], stage.sets[0]],
      value: stage.value,
    }));

    const result = [...data, ...additionalStages].map(item => ({
      sets: item.sets,
      customers: item.value,
    }));

    return result;
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: serializeTransactionStagesAPI.name,
      data: err.stack,
    });
    return [];
  }
};

export const adjustZoombarRangeAfterZoom = (segment, min, max) => {
  let { from, to } = segment;

  if (from < min) {
    from = min;
  }
  if (from > max) {
    from = max;
  }

  if (to < min) {
    to = min;
  }
  if (to > max) {
    to = max;
  }

  return { from, to };
};

export const segmentsToInputRanges = segments => {
  const result = segments.reduce((acc, segment) => {
    acc[segment.id] = { min: segment.from, max: segment.to };

    return acc;
  }, {});

  return result;
};

export const getZoombarRangeFromSegments = segments => {
  const ranges = getRangesFromSegments(segments);

  const min = ranges.at(0);
  const max = ranges.at(-1);

  return [min, max];
};

export const getZoombarRangeFromAggregation = aggregation => {
  const values = Object.values(aggregation);

  const min = Math.min(...values.map(item => item.min));
  const max = Math.max(...values.map(item => item.max));

  return [min, max];
};

export const getSegmentsByTrainingKey = trainingKey => {
  return Object.values(SEGMENTS).filter(
    segment => segment.trainingKey === trainingKey,
  );
};

export const validateInputRanges = inputRanges => {
  try {
    const transactionOrderSegmentIds = getSegmentsByTrainingKey(
      TRAINING_STEP_KEYS.TransactionOrder,
    ).map(segment => segment.id);

    const isValid = Object.entries(inputRanges).every(([segmentId, range]) => {
      if (!transactionOrderSegmentIds.includes(+segmentId)) {
        return typeof range.min === 'number' && typeof range.max === 'number';
      }

      return true;
    });

    return isValid;
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: validateInputRanges.name,
      data: err.stack,
    });

    return false;
  }
};

export const handleInitTrainModel = ({
  modelDetail,
  trainModelState,
  initialTrainModelState,
  ...otherArgs
}) =>
  produce(trainModelState, _draft => {
    if (isEmpty(modelDetail)) return initialTrainModelState;

    if (otherArgs.isInitFromNoti && otherArgs.isCreatePage) {
      return initialTrainModelState;
    }

    const trainModel = get(modelDetail, 'properties.trainModel', null);

    return isEmpty(trainModel) ? initialTrainModelState : trainModel;
  });

export const updateTrainingInfos = (contents, value) =>
  Object.keys(contents)
    .map(key => [
      { path: `contents.${key}.trained`, value: !value },
      { path: `contents.${key}.needToReTraining`, value },
      { path: `contents.${key}.isTraining`, value },
    ])
    .flat();

export const getPieChartProps = trainingKey => {
  if (trainingKey === TRAINING_STEP_KEYS.CustomerStage) {
    return {
      opts: {
        height: 360,
        width: 550,
      },
      center: ['251px', '50%'],
    };
  }

  return {
    opts: {
      height: 260,
      width: 462,
    },
    center: ['210px', '50%'],
  };
};
