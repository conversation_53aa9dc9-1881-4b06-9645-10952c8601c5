/* eslint-disable arrow-body-style */
/* eslint-disable indent */
/* eslint-disable no-param-reassign */
/* eslint-disable consistent-return */
/* eslint-disable no-plusplus */
import moment from 'moment';
import produce from 'immer';
import z from 'zod';
import {
  get,
  pick,
  sumBy,
  sum,
  maxBy,
  isEmpty,
  omit,
  min,
  max,
  fromPairs,
  map,
  orderBy,
} from 'lodash';
import { Map, OrderedMap } from 'immutable';
import { format } from 'date-fns';
import { getTranslateMessage } from 'containers/Translate/util';
import TRANSLATE_KEY from 'messages/constant';
import { getObjectPropSafely, safeParse } from 'utils/web/utils';
import {
  ATTRIBUTES_APPLY,
  MAX_RANGE_LIMIT_TYPE,
  SCORING_TYPE,
  TRAINING_STEP_KEYS,
} from './config';
import { safeParseArrayNumber } from 'utils/web/attribute';
import { toRefinePropertiesAPI } from 'components/common/UIPerformEventV2/BlockConditionPerformEvent/Refine/utils';
import { shortenNumber } from 'utils/number';
import { getCurrentUserId } from 'utils/web/cookie';
import DEFAULT_CHART_DATA from './defaultChartsData';
import { TRAINING_MODE } from '../../config';
import { MODEL_MATRIX, PERSONAS_KEY, PERSONAS, MODELS } from '../../../config';
import { addMessageToQueue } from 'utils/web/queue';
import {
  generateObjectApplyOptions,
  refinePropertiesAPIToCondtionsAPI,
  toAPIDataUpdate,
  toFePersona,
} from '../../utils';
import { STATUS_ITEM_CODE } from '../../../../../../../utils/constants';

export const NOTI = {
  error: msg => ({
    id: 'rfm_model_error',
    message: msg || TRANSLATE_MAP.errorMsg.prepareDataFail,
    timeout: 1500,
    timestamp: new Date().getTime(),
    type: 'error',
  }),
};

export const COLOR_BY_SCORE = {
  1: '#826DC5',
  2: '#FBAE2B',
  3: '#76C969',
  4: '#E67880',
  5: '#5B9EDE',
};

export const TRANSLATE_MAP = {
  tabs: {
    predictiveModels: getTranslateMessage(
      TRANSLATE_KEY.KHONG_CO,
      'Predictive Models',
    ),
    computationHistory: getTranslateMessage(
      TRANSLATE_KEY.KHONG_CO,
      'Computation History',
    ),
  },
  errorMsg: {
    prepareDataFail: getTranslateMessage(
      TRANSLATE_KEY.KHONG_CO,
      'Prepare data fail!!!',
    ),
    required: getTranslateMessage(
      TRANSLATE_KEY.KHONG_CO,
      "This field can't be empty",
    ),
    filterEmpty: getTranslateMessage(
      TRANSLATE_KEY.KHONG_CO,
      'Value filter empty',
    ),
    rfmNameExists: getTranslateMessage(
      TRANSLATE_KEY.KHONG_CO,
      'RFM name already exists',
    ),
    nameExist: getTranslateMessage(
      TRANSLATE_KEY.KHONG_CO,
      'This name is duplicated',
    ),
    nonVisualizable: getTranslateMessage(
      TRANSLATE_KEY.KHONG_CO,
      'Data can’t be visualized',
    ),
    invalidLabel: getTranslateMessage(TRANSLATE_KEY.KHONG_CO, 'Invalid label'),
    filterTimeRange: {
      endBeforeStart: getTranslateMessage(
        TRANSLATE_KEY.KHONG_CO,
        'End date is earlier than start date',
      ),
    },
  },
};

export const REPEAT_BY_OPTIONS = {
  none: { label: 'One', value: 'none' },
  real_time: { label: 'Real-time', value: 'real_time' },
  hours: { label: 'Hour', value: 'hours', min: 1, max: 24 },
  days: { label: 'Day', value: 'days', min: 1, max: 365 },
  weeks: { label: 'Week', value: 'weeks', min: 1, max: 52 },
  months: { label: 'Month', value: 'months', min: 1, max: 12 },
};

export const OPERATORS_OPTION = {
  GREATER_THAN: {
    label: 'is greater than',
    translateCode: TRANSLATE_KEY._OPERATOR_GREATER_THAN,
    value: 'greater_than',
  },
  GREATER_THAN_EQUAL: {
    label: 'is greater than or equal',
    translateCode: TRANSLATE_KEY._OPERATOR_GREATER_OR_EQUAL,
    value: 'greater_than_equal',
  },
  LESS_THAN: {
    label: 'is less than',
    translateCode: TRANSLATE_KEY._OPERATOR_LESS_THAN,
    value: 'less_than',
  },
  LESS_THAN_EQUAL: {
    label: 'is less than or equal',
    translateCode: TRANSLATE_KEY._OPERATOR_LESS_OR_EQUAL,
    value: 'less_than_equal',
  },
  NOT_EQUALS: {
    label: 'is not equal',
    translateCode: TRANSLATE_KEY._OPERATOR_NOT_EQUAL,
    value: 'not_equals',
  },
  EQUALS: {
    label: 'is equal',
    translateCode: TRANSLATE_KEY._OPERATOR_EQUAL,
    value: 'equals',
  },
  BETWEEN: {
    label: 'is between',
    translateCode: TRANSLATE_KEY._OPERATOR_BETWEEN,
    value: 'between',
  },
  EXISTS: {
    label: 'exists',
    translateCode: TRANSLATE_KEY._OPERATOR_EXIST,
    value: 'exists',
  },
  NOT_EXISTS: {
    label: 'does not exist',
    translateCode: TRANSLATE_KEY._OPERATOR_NOT_EXIST,
    value: 'not_exists',
  },
  CONTAINS: {
    label: 'contains',
    translateCode: TRANSLATE_KEY._OPERATOR_CONTAIN,
    value: 'contains',
  },
  DOESNT_CONTAIN: {
    label: 'does not contain',
    translateCode: TRANSLATE_KEY._OPERATOR_NOT_CONTAIN,
    value: 'doesnt_contain',
  },
  START_WITH: {
    label: 'starts with',
    translateCode: TRANSLATE_KEY._OPERATOR_START_WITH,
    value: 'start_with',
  },
  NOT_START_WITH: {
    label: "doesn't start with",
    translateCode: TRANSLATE_KEY._OPERATOR_NOT_START_WITH,
    value: 'not_start_with',
  },
  END_WITH: {
    label: 'ends with',
    translateCode: TRANSLATE_KEY._OPERATOR_END_WITH,
    value: 'end_with',
  },
  NOT_END_WITH: {
    label: "doesn't end with",
    translateCode: TRANSLATE_KEY._OPERATOR_NOT_END_WITH,
    value: 'not_end_with',
  },
  BEFORE_DATE: {
    label: 'is before date',
    translateCode: TRANSLATE_KEY._OPERATOR_BEFORE_DATE,
    value: 'before_date',
  },
  AFTER_DATE: {
    label: 'is after date',
    translateCode: TRANSLATE_KEY._OPERATOR_AFTER_DATE,
    value: 'after_date',
  },
  EQUAL_TIME_AGO: {
    label: 'is equal time ago',
    translateCode: TRANSLATE_KEY._OPERATOR_EQUAL_TIME_AGO,
    value: 'equal_time_ago',
  },
  NOT_EQUAL_TIME_AGO: {
    label: "isn't equal time ago",
    translateCode: TRANSLATE_KEY._OPERATOR_NOT_EQUAL_TIME_AGO,
    value: 'not_equal_time_ago',
  },
  BEFORE_TIME_AGO: {
    label: 'is before time ago',
    translateCode: TRANSLATE_KEY._OPERATOR_BEFORE_TIME_AGO,
    value: 'before_time_ago',
  },
  AFTER_TIME_AGO: {
    label: 'is after time ago',
    translateCode: TRANSLATE_KEY._OPERATOR_AFTER_TIME_AGO,
    value: 'after_time_ago',
  },
  BETWEEN_TIME_AGO: {
    label: 'is between time ago',
    translateCode: TRANSLATE_KEY._OPERATOR_BETWEEN_TIME_AGO,
    value: 'between_time_ago',
  },
  MATCHES: {
    label: 'matches any of',
    translateCode: TRANSLATE_KEY._OPERATOR_MATCH_ANY,
    value: 'matches',
  },
  NOT_MATCHES: {
    label: "doesn't match any of",
    translateCode: TRANSLATE_KEY._OPERATOR_NOT_MATCH_ANY,
    value: 'not_matches',
  },
  INCLUDES: {
    label: 'includes',
    translateCode: TRANSLATE_KEY._OPERATOR_INCLUDE,
    value: 'includes',
  },
  DOESNT_INCLUDE: {
    label: 'does not include',
    translateCode: TRANSLATE_KEY._OPERATOR_NOT_INCLUDE,
    value: 'doesnt_include',
  },
};

export const PERF_EVENT_OPERATORS = [
  OPERATORS_OPTION.GREATER_THAN_EQUAL,
  OPERATORS_OPTION.LESS_THAN_EQUAL,
  OPERATORS_OPTION.EQUALS,
  OPERATORS_OPTION.NOT_EQUALS,
  OPERATORS_OPTION.LESS_THAN,
  OPERATORS_OPTION.GREATER_THAN,
  OPERATORS_OPTION.BETWEEN,
];

export const defaultRepeatByOptions = [
  REPEAT_BY_OPTIONS.none,
  // REPEAT_BY_OPTIONS.real_time,
  // REPEAT_BY_OPTIONS.hours,
  REPEAT_BY_OPTIONS.days,
  REPEAT_BY_OPTIONS.weeks,
  REPEAT_BY_OPTIONS.months,
];

export const END_TIME_OPTIONS = {
  NEVER: 'none',
  ON: 'on_day',
  AFTER: 'after_num_running',
};

export const FORMAT_FULL_DATE = 'YYYY-MM-DD HH:mm:ss';
export const FORMAT_DATE = 'YYYY-MM-DD';
export const DEFAULT_START_TIME_HOUR = '06:00';

export const currentTime = moment(new Date(), FORMAT_FULL_DATE);
export const defaultFullStartTime = moment(new Date(), FORMAT_FULL_DATE).set({
  hour: 2,
  minute: 0,
  second: 0,
});

export const defaultStartTime = {
  day:
    // If currentTime > currentDate 02:00:00 => currentTime + 1 day
    currentTime > defaultFullStartTime
      ? defaultFullStartTime.add(1, 'days').format(FORMAT_DATE)
      : defaultFullStartTime.format(FORMAT_DATE),
  hour: DEFAULT_START_TIME_HOUR,
};

export const defaultEndTime = {
  day: moment(defaultStartTime.day, FORMAT_DATE)
    .add(7, 'days')
    .format(FORMAT_DATE),
  hour: '02:00',
};

export const defaultComputationSchedule = {
  repeatType: REPEAT_BY_OPTIONS.weeks.value,
  repeatOnValue: [1],
  repeatValue: 1,
  repeatStartTime: defaultStartTime,
  endTime: {
    type: END_TIME_OPTIONS.NEVER,
    numRunning: 1,
    onDay: defaultEndTime,
  },
};

export function initValueComputeSchedule() {
  return {
    type: 'dynamic',
    isLoading: false,
    ...defaultComputationSchedule,
  };
  // {
  //   "time_unit": "day" || "hour" ,
  //   "value": 1,
  //   "type":"static/dynamic"
  // }
}

export const getNotificationDefault = () => ({
  alertAccountIds: [+getCurrentUserId()],
  alertScopes: {
    success: [],
    failure: ['email', 'app_push'],
  },
});

export const mapDatatoSelect = (data, type = 'perf_event') => {
  try {
    if (Array.isArray(data)) {
      if (type === 'perf_event') {
        return data.map(item => {
          return {
            ...item,
            label: item.eventNameDisplay,
            value: item.eventTrackingCode,
            type,
          };
        });
      }
      const result = data
        .filter(element => element.status !== STATUS_ITEM_CODE.ARCHIVED)
        .map(item => {
          return {
            ...item,
            label: item.itemTypeDisplay,
            value: item.itemTypeName,
            type,
          };
        });

      return orderBy(result, ['label']);
    }
    return [];
  } catch (e) {
    return [];
  }
};

const mapItemChild = data => {
  const result = {
    customer: [],
    order: [],
    date: [],
    revenue: [],
  };
  try {
    if (Array.isArray(data.items)) {
      data.items.forEach(item => {
        if (+item.status !== 4) {
          if (item.dataType === 'string') {
            result.customer.push({
              ...item,
              label: item.translateLabel,
              value: `${item.itemPropertyName}${item.itemTypeId}`,
              type: 'item',
              valueRef: item.itemPropertyName,
            });
            result.order.push({
              ...item,
              label: item.translateLabel,
              value: `${item.itemPropertyName}${item.itemTypeId}`,
              type: 'item',
              valueRef: item.itemPropertyName,
            });
          } else if (item.dataType === 'datetime') {
            result.date.push({
              ...item,
              label: item.translateLabel,
              value: `${item.itemPropertyName}${item.itemTypeId}`,
              type: 'item',
              valueRef: item.itemPropertyName,
            });
          } else if (
            item.displayFormat &&
            item.displayFormat.type === 'CURRENCY'
          ) {
            result.revenue.push({
              ...item,
              label: item.translateLabel,
              value: `${item.itemPropertyName}${item.itemTypeId}`,
              type: 'item',
              valueRef: item.itemPropertyName,
            });
          }
        }
      });
      return result;
    }
    return result;
  } catch (e) {
    return result;
  }
};

export const toMapdataMultiple = (data, type) => {
  const dataOut = {
    customer: [],
    date: [],
    order: [],
    revenue: [],
  };
  try {
    if (Array.isArray(data)) {
      if (type === 'perf_event') {
        data.forEach(item => {
          if (+item.status !== 4) {
            if (item.items) {
              const { customer, date, order, revenue } = mapItemChild(item);
              if (customer.length) {
                dataOut.customer.push({
                  label: item.eventPropertyDisplay || item.itemPropertyDisplay,
                  value: null,
                  key: item.eventPropertyName || item.itemPropertyName,
                  itemTypeName: item.itemTypeName,
                  status: item.status,
                  options: customer,
                });
              }
              if (order.length) {
                dataOut.order.push({
                  label: item.eventPropertyDisplay || item.itemPropertyDisplay,
                  value: null,
                  key: item.eventPropertyName || item.itemPropertyName,
                  itemTypeName: item.itemTypeName,
                  status: item.status,
                  options: order,
                });
              }
              if (date.length) {
                dataOut.date.push({
                  label: item.eventPropertyDisplay || item.itemPropertyDisplay,
                  value: null,
                  key: item.eventPropertyName || item.itemPropertyName,
                  itemTypeName: item.itemTypeName,
                  status: item.status,
                  options: date,
                });
              }
              if (revenue.length) {
                dataOut.revenue.push({
                  label: item.eventPropertyDisplay || item.itemPropertyDisplay,
                  value: null,
                  key: item.eventPropertyName || item.itemPropertyName,
                  itemTypeName: item.itemTypeName,
                  status: item.status,
                  options: revenue,
                });
              }
            } else if (item.dataType === 'string') {
              dataOut.customer.push({
                label: item.eventPropertyDisplay || item.itemPropertyDisplay,
                value: item.eventPropertyName || item.itemPropertyName,
                key: item.eventPropertyName || item.itemPropertyName,
                itemTypeName: item.itemTypeName,
                status: item.status,
              });
              dataOut.order.push({
                label: item.eventPropertyDisplay || item.itemPropertyDisplay,
                value: item.eventPropertyName || item.itemPropertyName,
                key: item.eventPropertyName || item.itemPropertyName,
                itemTypeName: item.itemTypeName,
                status: item.status,
              });
            } else if (item.dataType === 'datetime') {
              dataOut.date.push({
                label: item.eventPropertyDisplay || item.itemPropertyDisplay,
                value: item.eventPropertyName || item.itemPropertyName,
                key: item.eventPropertyName || item.itemPropertyName,
                itemTypeName: item.itemTypeName,
                status: item.status,
              });
            } else if (
              item.displayFormat &&
              item.displayFormat.type === 'CURRENCY'
            ) {
              dataOut.revenue.push({
                label: item.eventPropertyDisplay || item.itemPropertyDisplay,
                value: item.eventPropertyName || item.itemPropertyName,
                key: item.eventPropertyName || item.itemPropertyName,
                itemTypeName: item.itemTypeName,
                status: item.status,
              });
            }
          }
        });
      } else if (type === 'comp_attr') {
        data.forEach(item => {
          if (+item.status !== 4) {
            if (item.data_type === 'string') {
              dataOut.customer.push({
                label: item.item_property_display,
                value: item.item_property_name,
                key: item.item_property_name,
                status: item.status,
              });
              dataOut.order.push({
                label: item.item_property_display,
                value: item.item_property_name,
                key: item.item_property_name,
                status: item.status,
              });
            } else if (item.data_type === 'datetime') {
              dataOut.date.push({
                label: item.item_property_display,
                value: item.item_property_name,
                key: item.item_property_name,
                status: item.status,
              });
            } else if (
              item.display_format &&
              item.display_format.type === 'CURRENCY'
            ) {
              dataOut.revenue.push({
                label: item.item_property_display,
                value: item.item_property_name,
                key: item.item_property_name,
                status: item.status,
              });
            }
          }
        });
      }
      return dataOut;
    }
    return dataOut;
  } catch (err) {
    return dataOut;
  }
};

function toConditionAPI(eventSelect, condition) {
  let tempt = {};
  const item = new Map(condition);

  let insightPropertyIds = [];

  let sourceInfo = eventSelect;

  const dataSources = safeParse(item.get('dataSources'), []);

  if (Array.isArray(dataSources) && dataSources.length > 0) {
    insightPropertyIds = safeParseArrayNumber(dataSources);
  }

  if (sourceInfo.type === 'perf_event') {
    sourceInfo = {
      ...pick(sourceInfo, [
        'eventCategoryId',
        'eventActionId',
        'eventTrackingName',
      ]),
      insightPropertyIds,
      condition_type: 'perf_event',
    };
  }

  if (sourceInfo.type === 'comp_attr') {
    sourceInfo = {
      ...pick(sourceInfo, ['itemTypeId', 'itemTypeName']),
      condition_type: 'bo',
    };
  }

  tempt = {
    ...sourceInfo,
    filters: toRefinePropertiesAPI(item.get('refineWithProperties')),
  };

  return tempt;
}

export const toAPIPersonasConfig = configs =>
  Object.entries(configs).reduce((acc, [key, { scores }]) => {
    acc[key] = scores;

    return acc;
  }, {});

export function toAPIPrepareData(prepareData) {
  const { eventSelect, multipleSelect, conditions, timeRange } = prepareData;

  const sourceType =
    get(eventSelect, 'type') === 'perf_event' ? 'events' : 'bo';

  const refineWithPropertiesAPI = toRefinePropertiesAPI(
    get(conditions, 'refineWithProperties'),
  );

  return {
    source_type: sourceType,

    ...(sourceType === 'events' && {
      event_category_id: get(eventSelect, 'eventCategoryId'),
      event_action_id: get(eventSelect, 'eventActionId'),
    }),

    data_source: get(eventSelect, 'value'),
    property_ids: get(conditions, 'dataSources', []).toString(),

    start_date: get(timeRange, 'startDate.date'),
    end_date: get(timeRange, 'endDate.date'),

    conditions: JSON.stringify({
      rules: {
        ...refinePropertiesAPIToCondtionsAPI(
          refineWithPropertiesAPI,
          sourceType,
        ),
      },
    }),

    ...toAPIMultipleSelect(multipleSelect, sourceType),
  };
}

export const toAPISourceType = sourceType => {
  const result = '';
  if (sourceType === 'perf_event' || sourceType === 'events') return 'events';
  if (sourceType === 'comp_attr' || sourceType === 'bo') return 'bo';

  return result;
};

// sourceAPIType: events, bo
export function toAPIMultipleSelect(multipleSelect, sourceType) {
  const apiScoreType = toAPISourceType(sourceType);

  function withItemTypeName(item) {
    const value = get(item, 'value', '');

    if (apiScoreType === 'bo') {
      return value;
    }

    const itemTypeName = get(item, 'itemTypeName', '');
    return itemTypeName ? `${itemTypeName}_${value}` : `events_${value}`;
  }

  return {
    customer_id: withItemTypeName(get(multipleSelect, 'customerIdentify')),
    date: withItemTypeName(get(multipleSelect, 'date')),
    order: withItemTypeName(get(multipleSelect, 'order')),
    revenue: withItemTypeName(get(multipleSelect, 'revenue')),
  };
}

export function toAPIData(settings) {
  const { prepareData, trainModel, applyModel } = settings;
  const { matrix } = trainModel;

  const timeRange = omit(get(prepareData, 'timeRange'), ['mode']);

  const recency = trainModel[SCORING_TYPE.recency];
  const frequency = trainModel[SCORING_TYPE.frequency];
  const monetary = trainModel[SCORING_TYPE.monetary];

  const {
    computeSchedule,
    notificationSetup,
    dataUpdate,
    triggerEvent,
  } = applyModel;

  const multipleSelect = toAPIMultipleSelect(
    get(prepareData, 'multipleSelect', {}),
    get(prepareData, 'eventSelect.type'),
  );

  const data = {
    modelType: +MODELS.RFM_MODEL,
    dataCondition: {
      ...toConditionAPI(
        get(prepareData, 'eventSelect'),
        get(prepareData, 'conditions'),
      ),

      event_select: get(prepareData, 'eventSelect'),

      customer_attribute_code: multipleSelect.customer_id,
      date_attribute_code: multipleSelect.date,
      order_attribute_code: multipleSelect.order,
      revenue_attribute_code: multipleSelect.revenue,

      time_range: timeRange,

      condition: get(prepareData, 'conditions'),
      multip_select: get(prepareData, 'multipleSelect'),
    },
    modelSubType: matrix,
    modelConfig: {
      personas: toAPIPersonasConfig(get(trainModel, 'personas.configs', {})),
      ranges: {
        r_input_ranges: get(recency, 'ranges'),
        f_input_ranges: get(frequency, 'ranges'),
        m_input_ranges: get(monetary, 'ranges'),
      },
      overwrites: {
        r_overwrite: {
          date: timeRange,
          granularity: get(recency, 'overWrite.granularity.value'),
        },
        f_overwrite: {
          date: timeRange,
          aggregation: get(frequency, 'overWrite.aggregation.value'),
        },
        m_overwrite: {
          date: timeRange,
          aggregation: get(monetary, 'overWrite.aggregation.value'),
        },
      },
    },
    alertSetting: notificationSetup,
    computeSchedule,
    dataUpdate: toAPIDataUpdate(dataUpdate),

    properties: {
      trainModel,
      applyModel: {
        triggerEvent,
      },
    },
  };

  return data;
}

export const serializePersonasDataAPI = (data, personasKeys) =>
  Object.fromEntries(
    personasKeys.map(key => [
      key,
      get(data, `personas.${key}`) ? data.personas[key] : 0,
    ]),
  );

export function getColorListFromRanges(ranges) {
  const rangeList = Object.entries(ranges);

  rangeList.sort((a, b) => a[1].min - b[1].min);

  const colorList = rangeList.map(item => item[1].color);

  return colorList;
}

export function mergeRangesWithChartRanges(ranges, chartRanges, options) {
  const { reverse = false } = options;

  try {
    let rangeEntries = Object.entries(ranges);

    if (rangeEntries.length + 1 > chartRanges.length) throw Error;

    rangeEntries = rangeEntries
      .sort(([scoreA], [scoreB]) =>
        reverse ? scoreB - scoreA : scoreA - scoreB,
      )
      .map(([score, value], index) => [
        score,
        {
          ...value,
          min: chartRanges[index],
          max: chartRanges[index + 1],
        },
      ]);

    return Object.fromEntries(rangeEntries);
  } catch (err) {
    return ranges;
  }
}

export const inputRangesToBarchartRange = inputRanges => {
  try {
    const values = Object.values(inputRanges);

    const mins = values
      .map(({ min: minValue }) => minValue)
      .sort((a, b) => a - b);

    return [...mins, get(maxBy(values, 'max'), 'max', 0)];
  } catch (err) {
    return new Array(Object.keys(inputRanges).length).fill(0);
  }
};

export const genBarChart = ({ maxRangeLimit, ranges, dataRanges, zoombar }) => {
  const range = inputRangesToBarchartRange(rangesToInputRanges(ranges));

  const arrBarChartData = mapDataRangesToDataBarchart(dataRanges);

  const config = {
    tooltip: {
      trigger: 'item',
      axisPointer: { type: 'shadow' },
    },
  };

  return {
    maxRangeLimit,
    range,
    color: getColorListFromRanges(ranges),
    data: arrBarChartData,
    zoombar,
    config,
  };
};

export const genTreeMap = ({
  configs,
  timeRange,
  values,
  rRanges,
  fRanges,
  mRanges,
  rGranularity,
}) => {
  try {
    const totalValue = sum(Object.values(values));

    const startDate = timeRange.startDate.date;
    const endDate = timeRange.endDate.date;

    const personas = Object.entries(configs).map(
      ([key, { scores: configScores }]) => {
        const [
          { min: rMin, max: rMax },
          { min: fMin, max: fMax },
          { min: mMin, max: mMax },
        ] = getMinMaxOfPersonasConfigScores(configScores);

        const rMinValue = rRanges[rMax].min;
        const rMaxValue = rRanges[rMin].max;

        const fMinValue = fRanges[fMin].min;
        const fMaxValue = fRanges[fMax].max;

        const mMinValue = mRanges[mMin].min;
        const mMaxValue = mRanges[mMax].max;

        return {
          id: key,
          name: key,
          rConfig: {
            granularity: rGranularity,
          },
          label: PERSONAS[key].label,
          color: PERSONAS[key].color,
          value: values[key],
          recency: {
            score: [rMin, rMax],
            value: [rMinValue, rMaxValue],
          },
          frequency: {
            score: [fMin, fMax],
            value: [fMinValue, fMaxValue],
          },
          monetary: {
            score: [mMin, mMax],
            value: [mMinValue, mMaxValue],
          },
        };
      },
    );

    const formatString = 'dd MMM yy';

    return {
      totalValue,
      personas,
      timeRange: [
        format(new Date(startDate), formatString),
        format(new Date(endDate), formatString),
      ],
    };
  } catch (error) {
    return DEFAULT_CHART_DATA.TREE_MAP.DATA;
  }
};

export const getRangesWithInputRanges = (inputRanges, uiRanges) => {
  // new min, max for all ranges
  const { min: newMin, max: newMax } = getMinMaxRangeFromInputRanges(
    inputRanges,
  );

  // old min, max for all ranges
  const { min: oldMin, max: oldMax } = getMinMaxRangeFromInputRanges(uiRanges);

  const inputRangeScores = Object.keys(inputRanges).map(Number);

  return Object.entries(uiRanges).reduce((acc, [key, value]) => {
    if (inputRangeScores.includes(+key)) {
      let rangeMin = inputRanges[key]?.min;
      let rangeMax = inputRanges[key]?.max;

      if (rangeMin <= oldMin) {
        rangeMin = min([rangeMin, newMin]);
      }

      if (rangeMax >= oldMax) {
        rangeMax = max([rangeMax, newMax]);
      }

      acc[key] = {
        ...value,
        min: rangeMin,
        max: rangeMax,
        value: get(inputRanges, `${key}.customers`),
      };
    }

    return acc;
  }, {});
};

export const mapDataRangesToDataBarchart = dataRanges =>
  Object.values(dataRanges).map(i => ({
    min: i.min,
    max: i.max,
    value: i.customers,
  }));

export const genPieChart = ({ scoreRange }) => ({
  option: {
    series: [
      {
        name: 'Simple',
        type: 'pie',
        radius: '90%',
        data: scoreRange.map(i => ({
          value: i.value,
          name: i.label,
          itemStyle: {
            color: i.color,
          },
        })),
        label: {
          show: true,
          position: 'inside',
          color: '#fff',
          formatter(params) {
            const { value } = params;
            const content = shortenNumber(Number(value));

            return content === '0' ? '' : content;
          },
          fontSize: 12,
        },
      },
    ],
  },
});

export const genScoreCard = ({ scoreType, ranges, errors = [] }) => {
  const mapErrors = fromPairs(
    errors.map(err => [
      err?.params?.score || '',
      {
        helperText: err.message,
      },
    ]),
  );

  const sumValue = sumBy(Object.values(ranges), 'value');

  let data = map(Object.entries(ranges), ([key, value]) => {
    const percent =
      value.value === 0
        ? 0
        : +Number((value.value / sumValue) * 100).toFixed(2);

    return {
      ...value,
      score: +key,
      percent,
      range: [value.min, value.max],

      ...(mapErrors[+key] && {
        error: true,
        helperText: mapErrors[+key].helperText,
      }),
    };
  });

  const MAPPING_SD = {
    [SCORING_TYPE.recency]: 'desc',
    [SCORING_TYPE.frequency]: 'asc',
    [SCORING_TYPE.monetary]: 'asc',
  };

  data = orderBy(data, ['score'], [MAPPING_SD[scoreType]]);

  data = map(data, (item, idx) => {
    const temp = {
      ...item,

      prefix: '',
      suffix: '',
    };

    const isFirst = idx === 0;
    const isEnd = idx === data.length - 1;

    if (isFirst) {
      temp.prefix = '≤ ';
    }

    if (isEnd) {
      temp.prefix = '> ';
    }

    // https://docs.google.com/presentation/d/1lKvQG3ee6dPphDw5v0NEK3tT3WwuA9lLZMv9_2Q0k7I/edit#slide=id.g1e51d24562d_0_336
    if (scoreType === SCORING_TYPE.monetary && idx !== 0) {
      temp.prefix = '> ';
    }

    // https://docs.google.com/presentation/d/1lKvQG3ee6dPphDw5v0NEK3tT3WwuA9lLZMv9_2Q0k7I/edit#slide=id.g226e807e675_0_98
    if (scoreType === SCORING_TYPE.frequency) {
      temp.suffix = ' times';

      if (!isFirst && !isEnd) {
        temp.range[0]++;
      }
    }

    if (scoreType === SCORING_TYPE.recency) {
      temp.suffix = ' days';

      if (!isFirst && !isEnd) {
        temp.range[0]++;
      }
    }

    return temp;
  });

  return { data };
};

export const updateRangesByScore = (ranges, score, updatedData) => {
  const entries = Object.entries(ranges).map(([key, value]) => [
    key,
    +score === +key ? { ...value, ...updatedData } : value,
  ]);

  return Object.fromEntries(entries);
};

export const initDefaultRanges = (matrix, currentRanges = {}) => {
  const entries = Object.entries(DEFAULT_CHART_DATA.RANGES[matrix]).map(
    ([key, value]) => [
      key,
      {
        ...value,
        ...pick(currentRanges[+key], ['label']),
      },
    ],
  );

  return Object.fromEntries(entries);
};

export const initDataRanges = (inputRanges = {}) => {
  Object.keys(inputRanges).forEach(score => {
    inputRanges[score].customers = 0;
  });

  return inputRanges;
};

export const getMinMaxOfPersonasConfigScores = scores => {
  const result = [];

  for (let i = 0; i < 3; i++) {
    const unitArr = scores.map(num => parseInt(num.toString()[i]));
    const minValue = Math.min(...unitArr);
    const maxValue = Math.max(...unitArr);

    result.push({ min: minValue, max: maxValue });
  }

  return result;
};

export const rangesToInputRanges = (ranges, additionalField = []) => {
  const entries = Object.entries(ranges).map(([key, value]) => [
    key,
    pick(value, ['min', 'max', ...additionalField]),
  ]);

  return Object.fromEntries(entries);
};

export function bindValidateToCondition(conditions, errors) {
  errors.forEach(item => {
    conditions = conditions.setIn([item.refineKey, 'error'], item.error);
  });

  return conditions;
}

export const mapRefineUI = data => {
  let temp = OrderedMap({});
  Object.keys(data).forEach(key => {
    temp = temp.set(
      key,
      OrderedMap({
        ...data[key],
      }),
    );
  });

  return temp;
};

export const getDefaultMultiple = (data, value, typeEvent) => {
  const output = {
    customer: {},
    date: {},
    order: {},
    revenue: {},
  };
  try {
    if (
      (value === 'transaction' || value === 'purchase') &&
      typeEvent === 'comp_attr'
    ) {
      const valueCustomer =
        Array.isArray(data.customer) &&
        data.customer.find(item => item.value === 'customer_id');
      const valueDate =
        Array.isArray(data.date) &&
        data.date.find(item => item.value === 'date_created');
      const valueOrder =
        Array.isArray(data.order) &&
        data.order.find(item => item.value === 'id');
      const valueRevenue =
        Array.isArray(data.revenue) &&
        data.revenue.find(item => item.value === 'revenue');
      output.customer = valueCustomer;
      output.date = valueDate;
      output.order = valueOrder;
      output.revenue = valueRevenue;
    } else if (
      (value === 'purchase_product' || value === 'transaction_event') &&
      typeEvent === 'perf_event'
    ) {
      const valueCustomer =
        Array.isArray(data.customer) &&
        data.customer.find(item => item.value === 'customer_id');
      //    ||
      // (data.customer && data.customer[0] && !data.customer[0].value
      //   ? data.customer.find(item => item.value)
      //   : data.customer[0]);
      const valueDate =
        Array.isArray(data.date) &&
        data.date.find(item => item.value === 'tracked_time');
      const valueOrder =
        Array.isArray(data.order) &&
        data.order.find(item => item.value === 'order_id');
      const valueRevenue =
        Array.isArray(data.revenue) &&
        data.revenue.find(item => item.value === 'revenue_currency');
      output.customer = valueCustomer;
      output.date = valueDate;
      output.order = valueOrder;
      output.revenue = valueRevenue;
    }
    return output;
  } catch (e) {
    return output;
  }
};

export const convertDataInput = data => {
  try {
    return {
      ...data,
      label: data.label,
      value: data.valueCustom ? data.valueCustom : data.value,
    };
  } catch {
    return data;
  }
};

export const getMinMaxRangeFromInputRanges = inputRanges => {
  const rangeValues = Object.values(inputRanges).reduce(
    (acc, value) => [...acc, value.min, value.max],
    [],
  );

  if (isEmpty(rangeValues)) return { min: 0, max: 0 };

  return { min: min(rangeValues), max: max(rangeValues) };
};

export const getScoreWithMaxValue = inputRanges => {
  const entries = Object.entries(inputRanges);

  const result = entries.reduce(
    (acc, [score, value]) => {
      if (acc.score === null || acc.max < value.max) {
        acc.score = +score;
        acc.max = value.max;
      }

      return acc;
    },
    {
      score: null,
      max: null,
    },
  );

  return result;
};

export const serializeCustomInputRanges = params => {
  const { apiRanges = {}, customRanges = {}, limitType } = params || {};

  const entries = Object.entries(customRanges).map(([score, value]) => [
    +score,
    {
      min: value.min,
      max: value.max,
      customers: apiRanges[score]?.customers || 0,
    },
  ]);

  const serializedRanges = Object.fromEntries(entries);

  if (limitType === MAX_RANGE_LIMIT_TYPE.gte) {
    const { max: maxValue, score } = getScoreWithMaxValue(apiRanges);

    if (serializedRanges[score]?.max < maxValue) {
      serializedRanges[score].max = maxValue;
    }
  }

  return serializedRanges;
};

export const handleInitTrainModel = ({
  modelDetail,
  trainModelState,
  isVersionHistory,
  initialTrainModelState,
  ...otherArgs
}) => {
  try {
    const result = produce(trainModelState, draft => {
      if (isEmpty(modelDetail)) {
        return initialTrainModelState;
      }

      const { modelConfig, properties, modelSubType } = modelDetail;

      const { ranges } = modelConfig;

      const trainModel = properties?.trainModel || trainModelState;

      trainModel.mode = trainModel.mode || TRAINING_MODE.Expert;
      trainModel.active = trainModel.active || TRAINING_STEP_KEYS.RECENCY;

      trainModel[SCORING_TYPE.recency].zoombar = toUIZoombar({
        zoombar: trainModel[SCORING_TYPE.recency].zoombar,
        inputRanges: ranges.r_input_ranges,
      });

      trainModel[SCORING_TYPE.frequency].zoombar = toUIZoombar({
        zoombar: trainModel[SCORING_TYPE.frequency].zoombar,
        inputRanges: ranges.f_input_ranges,
      });

      trainModel[SCORING_TYPE.monetary].zoombar = toUIZoombar({
        zoombar: trainModel[SCORING_TYPE.monetary].zoombar,
        inputRanges: ranges.m_input_ranges,
      });

      if (isVersionHistory) {
        return isEmpty(trainModel) ? initialTrainModelState : trainModel;
      }

      draft.mode = trainModel.mode;
      draft.active = trainModel.active;
      draft.matrix = modelSubType;
      draft.personas.configs = toFePersona(modelConfig.personas);

      // Zoombar
      draft[SCORING_TYPE.recency].zoombar =
        trainModel[SCORING_TYPE.recency].zoombar;

      draft[SCORING_TYPE.frequency].zoombar =
        trainModel[SCORING_TYPE.frequency].zoombar;

      draft[SCORING_TYPE.monetary].zoombar =
        trainModel[SCORING_TYPE.monetary].zoombar;

      // Ranges
      draft[SCORING_TYPE.recency].ranges = ranges.r_input_ranges;

      draft[SCORING_TYPE.frequency].ranges = ranges.f_input_ranges;

      draft[SCORING_TYPE.monetary].ranges = ranges.m_input_ranges;

      // Data Ranges
      draft[SCORING_TYPE.recency].dataRanges = initDataRanges(
        rangesToInputRanges(ranges.r_input_ranges),
      );

      draft[SCORING_TYPE.frequency].dataRanges = initDataRanges(
        rangesToInputRanges(ranges.f_input_ranges),
      );

      draft[SCORING_TYPE.monetary].dataRanges = initDataRanges(
        rangesToInputRanges(ranges.m_input_ranges),
      );

      if (otherArgs.isInitFromNoti && otherArgs.isCreatePage) {
        return draft;
      }

      draft[SCORING_TYPE.recency].isUsingDefaultAllChart = false;
      draft[SCORING_TYPE.frequency].isUsingDefaultAllChart = false;
      draft[SCORING_TYPE.monetary].isUsingDefaultAllChart = false;
    });

    return result;
  } catch (error) {
    addMessageToQueue({
      func: 'handleInitTrainModel',
      data: error.stack,
    });

    return null;
  }
};

export const toUIZoombar = ({ zoombar, inputRanges }) => {
  const Schema = z.object({
    range: z.tuple([z.number(), z.number()]),
    default: z.tuple([z.number(), z.number()]),
  });

  const validateResult = Schema.safeParse(zoombar);

  if (!validateResult.success) {
    const { min: minValue, max: maxValue } = getMinMaxRangeFromInputRanges(
      inputRanges,
    );

    return {
      range: [minValue, maxValue],
      default: [0, maxValue],
    };
  }

  return validateResult.data;
};

export const convertDataSelect = data => {
  let results = [];
  try {
    if (data && Array.isArray(data)) {
      results = data.map(item => {
        return {
          name: item.segmentDisplay,
          label: item.segmentDisplay,
          id: item.segmentId,
          value: item.segmentId,
          status: Number(item.status),
        };
      });
    }
    return results;
  } catch (e) {
    return [];
  }
};

export const getListSegmentByMatrix = mat => {
  const result = [];

  try {
    const personasByMatrix = getObjectPropSafely(
      () => MODEL_MATRIX[mat].defaultPersonas,
    );

    if (!personasByMatrix) {
      throw new Error(`Cannot find RFM Personas with matrix: ${mat}`);
    }

    Object.keys(personasByMatrix).forEach(personasKey => {
      const id = PERSONAS_KEY[personasKey];

      result.push({
        id,
        name: id,
        label: PERSONAS[personasKey].label,
      });
    });

    return result;
  } catch (error) {
    return result;
  }
};

export const genRFMSegments = ({ rfmMatrix, modelName }) => {
  const options = generateObjectApplyOptions({
    options: getListSegmentByMatrix(rfmMatrix),
    modelType: MODELS.RFM_MODEL,
    modelName,
  });

  return options;
};

export const getListApplyAttributes = () => {
  const defaultAttrs = ATTRIBUTES_APPLY.filter(attr => attr.id === 'personas');

  return defaultAttrs;
};

export const getRangeLimitType = ({ zoombar }) => {
  return get(zoombar, 'range.1') === get(zoombar, 'default.1')
    ? MAX_RANGE_LIMIT_TYPE.gte
    : MAX_RANGE_LIMIT_TYPE.eq;
};
