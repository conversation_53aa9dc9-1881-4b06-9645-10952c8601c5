/* eslint-disable indent */
/* eslint-disable no-param-reassign */
import { combineReducers } from 'redux';
import trainModelReducer from './trainModel';
import prepareDataReducerFor from './prepareData';
import applyModelReducerFor from '../../../reducers/applyModel';
import { MODELS } from '../../../../config';
import {
  getListApplyAttributes,
  getListSegmentByMatrix,
  genRFMSegments,
} from '../utils';
import { ATTRIBUTES_APPLY, DEFAULT_MATRIX } from '../config';
import { genAttributesApplyOptions } from '../../../utils';
import { TRIGGER_EVENT } from '../../../config';
import { applyModelReducer } from './applyModel';
import { pick } from 'lodash';

const modelType = MODELS.RFM_MODEL;

export default args => {
  return combineReducers({
    prepareData: prepareDataReducerFor(args),

    trainModel: trainModelReducer(args),

    applyModel: applyModelReducerFor({
      ...args,

      initialStateParams: {
        modelType,

        initDataUpdate: () => ({
          segments: genRFMSegments({
            rfmMatrix: DEFAULT_MATRIX,
          }),

          attributes: genAttributesApplyOptions({
            options: getListApplyAttributes(),
            modelType,
          }),

          segmentApplyOptions: getListSegmentByMatrix(DEFAULT_MATRIX).map(opt =>
            pick(opt, ['id', 'label', 'name']),
          ),

          attributeApplyOptions: ATTRIBUTES_APPLY.map(opt =>
            pick(opt, ['id', 'label', 'name']),
          ),
        }),

        fields: {
          triggerEvent: [
            {
              eventTrackingName: TRIGGER_EVENT.PersonasMovement,
            },
          ],
        },
      },

      customReducer: applyModelReducer,
    }),
  });
};
