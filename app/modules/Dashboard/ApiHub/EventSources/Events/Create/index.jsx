/* eslint-disable indent */
/* eslint-disable no-param-reassign */
/* eslint-disable prefer-destructuring */
/* eslint-disable react/prop-types */
import React, { useCallback, useEffect } from 'react';
import { connect } from 'react-redux';
import { useImmer } from 'use-immer';
import queryString from 'query-string';
import ErrorBoundary from 'components/common/ErrorBoundary';
import { UIStep } from '@xlab-team/ui-components';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import { CreateEventRoot, StepWrapper } from './styles';
import Design from './Design/index';
import UIHelmet from '../../../../../../components/Templates/LayoutContent/Helmet';
import CustomHeader from '../../../../../../components/Organisms/CustomHeader';
import { BREADCRUMDATA, STEP } from './config';
import {
  addNotification,
  getList,
  init,
  reset,
  update,
  updateValue,
} from '../../../../../../redux/actions';
import { MODULE_CONFIG as MODULE_CONFIG_SETTNGS } from './Design/Settings/config';
import {
  Button,
  DrawerDetail,
  EditableName,
  Icon,
  Steps,
} from '@antscorp/antsomi-ui';
import APP from '../../../../../../appConfig';
import { getPortalId } from '../../../../../../utils/web/cookie';
import { useHistory, useLocation, withRouter } from 'react-router-dom';
import HeaderDrawer from '../../../../../../components/common/HeaderDrawer';
import { createStructuredSelector } from 'reselect';
import makeSelectCreateSettings, {
  makeSelectCreateSettingsDataConfig,
} from './Design/Settings/selectors';
import reducer from './reducer';
import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';
import { compose } from 'redux';
import Footer from './Footer';
import { toEntryAPI } from './Design/Settings/utils';
import EventAttributeServices from 'services/EventAttribute';
import EventServices from 'services/Event';
import { getErrorMessageV2Translate } from '../../../../../../utils/web/message';
import AttributesPage from '../Detail/Attributes';
import {
  BoxHeaderLeft,
  BoxHeaderRight,
  StyledIcon,
  WrapperHeader,
} from '../../../../../../containers/Drawer/DrawerDetailDataObject/styled';
import { makeSelectDetailDomainMain } from '../Detail/selectors';
import { useUpdateObjectName } from '../../../../../../hooks';
import { TableIcon } from '@antscorp/antsomi-ui/es/components/icons';
import { getUntitledName } from '../../../../../../utils/web/properties';
import { isEmpty } from 'lodash';
import EditableContent from '../../../../../../components/common/EditableContent';

const MAP_TITLE = {
  titlDest: getTranslateMessage(TRANSLATE_KEY._TAB_EVENT_EVENT, 'Event'),
  createEvent: getTranslateMessage(
    TRANSLATE_KEY._BREADCRUMB_CREATE_EVENT_STEP1,
    'Create Event',
  ),
  assginEvent: getTranslateMessage(
    TRANSLATE_KEY._BREADCRUMB_CREATE_EVENT_STEP2,
    'Assgin Event Attributes',
  ),
  labelSave: getTranslateMessage(TRANSLATE_KEY._ACT_SAVE, 'Save'),
};
const MENUTABS = [
  {
    icon: <Icon type="icon-ants-pencil" />,
    label: 'Settings',
    key: 'settings',
  },
  {
    icon: <TableIcon />,
    label: 'Listing attributes',
    key: 'attributes',
  },
];
const STEPS = [
  { index: STEP.FILL_INFO, title: MAP_TITLE.createEvent },
  { index: STEP.ASSIGN_ATTR, title: MAP_TITLE.assginEvent },
];
const initState = {
  activeStep: STEP.FILL_INFO,
  eventActionId: null,
  eventCategoryId: null,
  isDoing: false,
  browserTitle: getUntitledName('Untitled Event'),
};
function CreateEvents(props) {
  const {
    main,
    dataConfig = {},
    design = 'create',
    eventActionId,
    eventCategoryId,
    tab,
    mainDetail,
    activeRow = {},
  } = props;
  const { isDoing, isLoadingConfigFields, assignEvent } = main;
  const history = useHistory();
  const location = useLocation();
  const [state, setState] = useImmer(initState);
  const searchParams = new URLSearchParams(location?.search);
  const id = searchParams.get('create-event') || null;
  const { createCopyEC, createCopyEA } = queryString.parse(
    window.location.search,
  );
  const {
    isLoading: isLoadingEventName,
    isError: isErrorEventName,
    error: errorEventName,
    updateName: updateEventName,
    isSuccess: isSuccessEventName,
  } = useUpdateObjectName({
    serviceFunc: EventServices.data.updateName,
    options: {},
  });
  const callback = (type, data = {}) => {
    switch (type) {
      case 'BACK_STEP': {
        setState(draft => {
          draft.activeStep = state.activeStep - 1;
        });
        break;
      }
      case 'NEXT_STEP': {
        const dataTmp = toEntryAPI(dataConfig);

        setState(draft => {
          draft.activeStep = state.activeStep + 1;
          draft.eventActionId = dataTmp.eventActionId;
          draft.eventCategoryId = dataTmp.eventCategoryId;
        });
        break;
      }
      case 'CREATE_EVENT': {
        setState(draft => {
          draft.activeStep = STEP.ASSIGN_ATTR;
          draft.eventActionId = data.eventActionId;
          draft.eventCategoryId = data.eventCategoryId;
        });
        break;
      }
      case 'ON_CANCEL': {
        props.goToList();
        break;
      }
      default:
        break;
    }
  };
  const handleCloseCreate = useCallback(() => {
    if (isSuccessEventName) {
      props.fetchData();
    }
    history.push(`${APP.PREFIX}/${getPortalId()}/api-hub/event-sources/events`);

    // resret state
    setState(() => initState);
  }, [isSuccessEventName]);
  const onChangeData = useCallback(
    name => value => {
      if (value !== dataConfig[name].value) {
        props.onChangeDataConfig({ name, value });
      }
    },
    [dataConfig],
  );
  const saveDataAssign = () => {
    const param = {
      eventCategoryId: state.eventCategoryId,
      eventActionId: state.eventActionId,
      data: assignEvent.data,
    };

    setState(draft => {
      draft.isDoing = true;
    });
    EventAttributeServices.data
      .assignAttrs(param)
      .then(res => {
        setState(draft => {
          draft.isDoing = false;
        });

        // console.log('res ===>', res);
        if (res.code === 200) {
          const notification = {
            message: getTranslateMessage(
              TRANSLATE_KEY._NOTIFICATION_SAVED_CHANGES_SUCCESS,
            ),
            timeout: 3000,
            type: 'success',
          };
          // props.addNotification(notification);
          handleCloseCreate();
        } else {
          const notification = {
            message: res.message,
            ...getErrorMessageV2Translate(
              TRANSLATE_KEY._NOTI_ASSIGN_ATTRIBUTE_ERROR,
              'Assign attributes error',
            ),
            timeout: 4000,
            type: 'danger',
          };
          props.addNotification(notification);
        }
      })
      .catch(e => {
        setState(draft => {
          draft.isDoing = false;
        });
        console.log('e ===>', e);
      });
  };
  const handleSaveOnAssignStep = () => {
    // save step 1
    if (state.activeStep === 0) {
      props.onSave({ onClose: handleCloseCreate });
    }
    // save step 2
    else if (state.activeStep === 1) {
      props.onSave({
        onSuccess: saveDataAssign,
        onFailure: () => {
          setState(draft => {
            draft.activeStep = state.activeStep - 1;
          });
        },
        onClose: handleCloseCreate,
      });
    }
  };

  const onChangeTab = value => {
    props.onChangeTab(value);
    setState(draft => {
      draft.activeStep = STEP.FILL_INFO;
    });
    const endParam = value;
    searchParams.set('tab', endParam);
    const newSearch = searchParams.toString();
    history.replace({ search: newSearch });
    // if (value === 'settings') {
    //   endParam = `${value}?design=${mainCreate.design}`;
    // }
  };
  const onBlurEventName = () => {
    if (isEmpty(mainDetail.valueObjectName.value.EN)) return;
    updateEventName({
      eventCategoryId,
      eventActionId,
      data: {
        eventNameDisplay: mainDetail.valueObjectName.value.EN,
      },
    });
  };
  const onChangeStep = data => {
    const validate = isDoing || isLoadingConfigFields || !dataConfig.isValidate;
    if (validate) {
      return;
    }
    const dataTmp = toEntryAPI(dataConfig);

    setState(draft => {
      draft.activeStep = data;
      draft.eventActionId = dataTmp.eventActionId;
      draft.eventCategoryId = dataTmp.eventCategoryId;
    });
  };
  const titleName =
    design === 'update'
      ? mainDetail.valueObjectName.value.EN
      : dataConfig?.eventName.value.EN;
  return (
    <>
      <UIHelmet
        title={
          isEmpty(titleName) && design !== 'update'
            ? 'Create new event'
            : titleName
        }
      />
      <DrawerDetail
        open={id || (eventActionId && eventCategoryId)}
        menuProps={{
          items:
            design === 'create'
              ? [
                  {
                    icon: <Icon type="icon-ants-pencil" />,
                    label: 'Create promotion pool',
                    key: 'settings',
                  },
                ]
              : MENUTABS,
          selectedKeys: tab,
          onClick: item => {
            if (item) {
              onChangeTab(item.key);
            }
          },
        }}
        headerProps={{
          showBorderBottom: true,
          height: '50px',
          style: {
            padding: '0 15px',
          },
          children: (
            <WrapperHeader>
              <BoxHeaderLeft>
                {design === 'create' ? (
                  <EditableName
                    value={dataConfig?.eventName.value.EN}
                    required
                    onChange={name => {
                      props.onChangeDataConfig({
                        name: 'eventName',
                        value: {
                          DEFAULT_LANG: 'EN',
                          EN: name,
                        },
                      });
                      // onChangeData('conversionName', {
                      //   DEFAULT_LANG: 'EN',
                      //   EN: name,
                      // })
                    }}
                    error={dataConfig.eventName.errors[0]}
                  />
                ) : (
                  <EditableContent
                    defaultValue={mainDetail.valueObjectName.value.EN}
                    required
                    onChange={name => {
                      props.onUpdateName(name);
                    }}
                    onBlur={onBlurEventName}
                    loading={isLoadingEventName}
                    isError={isErrorEventName}
                    error={errorEventName}
                  />
                )}
              </BoxHeaderLeft>
              <BoxHeaderRight>
                {(tab !== 'attributes' || design === 'create') && (
                  <Button
                    type="primary"
                    variant="contained"
                    onClick={handleSaveOnAssignStep}
                    disabled={
                      isDoing || isLoadingConfigFields || !dataConfig.isValidate
                    }
                    style={{ borderRadius: '10px' }}
                  >
                    {MAP_TITLE.labelSave}
                  </Button>
                )}
              </BoxHeaderRight>
            </WrapperHeader>
          ),
        }}
        onClose={handleCloseCreate}
        destroyOnClose
      >
        {/* <HeaderDrawer
        extraContent={
          tab !== 'attributes' && (
            <Button
              color="primary"
              variant="contained"
              onClick={handleSaveOnAssignStep}
              disabled={
                isDoing || isLoadingConfigFields || !dataConfig.isValidate
              }
              style={{ borderRadius: '10px' }}
            >
              {MAP_TITLE.labelSave}
            </Button>
          )
        }
      >
        {dataConfig.eventName?.componentEl({
          ...dataConfig.eventName,
          onChange: onChangeData,
        })}
      </HeaderDrawer> */}
        <ErrorBoundary path="app/modules/Dashboard/ApiHub/EventSources/Events/Create/index.jsx">
          {/* <UIHelmet title={MAP_TITLE.titlDest} />

        <CustomHeader
          showCancel
          breadcrums={BREADCRUMDATA}
          callback={callback}
          maxWidthBreadcrum="200px"
        /> */}
          {tab === 'settings' || design === 'create' ? (
            <>
              <StepWrapper>
                <Steps
                  current={state.activeStep}
                  items={STEPS}
                  iconSize={24}
                  onChange={onChangeStep}
                  // style={{ width: '30% !important' }}
                />
              </StepWrapper>
              <CreateEventRoot
                style={{ padding: state.activeStep === 1 && 'unset' }}
              >
                <Design
                  eventActionId={
                    design === 'create' ? state.eventActionId : eventActionId
                  }
                  eventCategoryId={
                    design === 'create'
                      ? state.eventCategoryId
                      : eventCategoryId
                  }
                  createCopyEC={createCopyEC}
                  createCopyEA={createCopyEA}
                  activeStep={state.activeStep}
                  callback={callback}
                  isDoing={state.isDoing}
                  design={design}
                  tab={tab}
                  valueObjectName={mainDetail.valueObjectName}
                  activeRow={activeRow}
                />
              </CreateEventRoot>
            </>
          ) : (
            <>
              <AttributesPage
                eventActionId={eventActionId}
                eventCategoryId={eventCategoryId}
              />
            </>
          )}
          {(tab !== 'attributes' || design === 'create') && (
            <Footer
              activeStep={state.activeStep}
              main={main}
              dataConfig={dataConfig}
              callback={callback}
            />
          )}
        </ErrorBoundary>
      </DrawerDetail>
    </>
  );
}
const mapStateToProps = createStructuredSelector({
  dataConfig: makeSelectCreateSettingsDataConfig(),
  main: makeSelectCreateSettings(),
  mainDetail: makeSelectDetailDomainMain(),
});

function mapDispatchToProps(dispatch) {
  return {
    init: params => {
      dispatch(init(MODULE_CONFIG_SETTNGS.key, params));
    },
    reset: params => {
      dispatch(reset(MODULE_CONFIG_SETTNGS.key, params));
    },
    goToList: value =>
      dispatch(updateValue(`${MODULE_CONFIG_SETTNGS.key}@@GO_TO_LIST`, value)),
    onSave: value => dispatch(update(MODULE_CONFIG_SETTNGS.key, value)),
    onChangeDataConfig: value =>
      dispatch(
        updateValue(`${MODULE_CONFIG_SETTNGS.key}@@DATA_CONFIG@@`, value),
      ),
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
    onChangeTab: params => {
      dispatch(updateValue(`${MODULE_CONFIG_SETTNGS.key}@@CHANGE_TAB`, params));
    },
    resetReducerDetail: params => {
      dispatch(reset(`event-detail`, params));
    },
    onUpdateName: value => {
      dispatch(updateValue(`event-detail@@UPDATE_NAME`, value));
    },
    fetchData: params => {
      dispatch(getList('event-list', params));
    },
  };
}

const withReducer = injectReducer({
  key: MODULE_CONFIG_SETTNGS.key,
  reducer,
});
const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

// const withReducer = injectReducer({
//   key: MODULE_CONFIG.key,
//   reducer,
// });

export default compose(
  withReducer,
  withRouter,
  withConnect,
)(CreateEvents);
