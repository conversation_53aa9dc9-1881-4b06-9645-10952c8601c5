/* eslint-disable no-else-return */
/* eslint-disable eqeqeq */
/* eslint-disable react/prop-types */
/* eslint-disable no-unused-vars */
import Icon from '@material-ui/core/Icon';
// import LayoutLoading from '../../../../../../components/Templates/LayoutContent/LayoutLoading';
import { getTranslateMessage } from 'containers/Translate/util';
import TRANSLATE_KEY from 'messages/constant';
import React from 'react';
// import PerfectScrollbar from 'react-perfect-scrollbar';
// import ScrollBar from 'react-scrollbars-custom';
// import { Resizable } from 're-resizable';
import { connect } from 'react-redux';
import { updateValue } from 'redux/actions';
import { createStructuredSelector } from 'reselect';
import { safeParse } from 'utils/common';
import MatchingFile from 'containers/Segment/Content/MatchingFile';
import Condition from 'containers/Segment/Content/Condition';
import Footer from 'containers/Segment/Content/Footer';
import GeneralSetting from 'containers/Segment/Content/GeneralSettings';
import ComputationSchedule from 'containers/Segment/Content/ComputationSchedule';
import NotificationSetup from 'containers/Segment/Content/NotificationSetup';
import Name from 'containers/Segment/Content/Name';
import SegmentMember from 'containers/Segment/Content/SegmentMember';
import { getCurrentUserId } from 'utils/web/cookie';
import CustomerDetail from '../../../../../../Profile/Customer/Detail/Preview';
import {
  DivMessageComputing,
  StyleContent,
  StyleScrollBar,
  WrapperContent,
  WrapperDetail,
  WrapperSegmentMembers,
} from '../../../../../../../../containers/Segment/Content/styles';
import { LayoutLoadingCreateSegment } from '../../../../../../Profile/Segment/Create/_UI/LayoutLoadingCreateSegment';
import VisitorDetail from '../../../../../../Profile/Visitor/Detail/Preview';
import {
  makeSelectConfigCreateSegment,
  makeSelectCreateSegmentCondition,
  makeSelectMainCreateSegment,
} from '../selectors';
import { ARCHIVE_STATUS } from '../../../../../../../../utils/constants';
import { StyledWrapperDisable } from '../styles';
import { makeSelectActiveRowCurrentVersion } from '../../Detail/selectors';
import WrapperDisable from '../../../../../../../../components/common/WrapperDisable';
import { UIWrapperDisable } from '@xlab-team/ui-components';
const closeBtnStyle = {
  width: '0',
  height: '0',
  position: 'absolute',
  top: '1rem',
  right: '2.25rem',
  zIndex: 1000,
};

const MAP_TITLE = {
  // Name
  name: getTranslateMessage(
    TRANSLATE_KEY._TITL_COLLECTION_NAME,
    'Collection Name',
  ),
  placeholderDescription: getTranslateMessage(
    TRANSLATE_KEY._USER_GUIDE_DESCRIBE_COLLECTION,
    'Describe your collection',
  ),

  // Condition
  include: getTranslateMessage(
    TRANSLATE_KEY._TITL_INCLUDE_ITEM,
    'Include item that',
  ),
  orInclude: getTranslateMessage(TRANSLATE_KEY._004, 'Or match item that'),

  // GeneralSetting
  generalSetting: getTranslateMessage(
    TRANSLATE_KEY._TITL_UPDATE_COLLECTION,
    'Update collection',
  ),
  static: getTranslateMessage(
    TRANSLATE_KEY._TITL_STATIC_COLLECTION,
    'Static Collection - no update',
  ),
  dynamic: getTranslateMessage(
    TRANSLATE_KEY._TITL_DYNAMIC_COLLECTION,
    'Dynamic Collection - update every',
  ),

  // SegmentMember
  segmentMember: getTranslateMessage(
    TRANSLATE_KEY._TITL_COLLECTION_ITEM,
    'Collection item',
  ),
  limit: getTranslateMessage(
    TRANSLATE_KEY._COLLECTION_ITEM_LIMIT,
    'Sort collection, then get top of',
  ),
  limitNotice: getTranslateMessage(
    TRANSLATE_KEY._USER_GUIDE_NOTICE_LIMIT_SEGMENT,
    '<b>Notice:</b> The limit setting is not applied for the audiences that journeys automatically add to this segment later.',
  ),
  member: getTranslateMessage(TRANSLATE_KEY._TITL_ITEM, 'item(s)'),

  // MatchingFile
  titlUploadFile: getTranslateMessage(
    TRANSLATE_KEY._TITL_UPLOAD_ITEM_FILE,
    'Upload your items file',
  ),
  titlSettings: getTranslateMessage(
    TRANSLATE_KEY._USER_GUIDE_COLLECTION_CREATE_CRITERIA,
    'Create criteria to find suitable items for this collection, using into from your file',
  ),
  titlDescription: getTranslateMessage(
    TRANSLATE_KEY._USER_GUIDE_COLLECTION_CREATE_CRITERIA_INTRO,
    'This system will look for items satisfying your criteria and add them to this collection',
  ),
  titlNote: getTranslateMessage(
    TRANSLATE_KEY._USER_GUIDE_COLLECTION_CREATE_CRITERIA_NOTE,
    'Note: From the uploaded file, only item which ID is existing in this system can be added to the to-be-created collection',
  ),
  addMore: getTranslateMessage(TRANSLATE_KEY._ACT_ADD_CRITERIA, 'Add criteria'),
  updateMethod: getTranslateMessage(
    TRANSLATE_KEY._TITL_CHOOSE_UPDATE_METHOD,
    'Choose update method',
  ),

  // ComputationSchedule
  scheduleTitle: getTranslateMessage(
    TRANSLATE_KEY.DATLE,
    'Computation Schedule',
  ),
  title: getTranslateMessage(
    TRANSLATE_KEY._TITL_OFFEN_COLLECTION_UPDATE,
    'How often do you want your collection to be updated?',
  ),
};

function Content(props) {
  // const [width, setWidth] = React.useState('70%');
  // const [height, setHeight] = React.useState(200);
  const {
    width,
    configure,
    isValidTotalLimit,
    main,
    design,
    isOpenDetail,
    activeItem,
    isVersion = false,
  } = props;
  const { activeRow = {}, checkPermision = {} } = main;
  const { isEdit = true, isView = true } = checkPermision;
  const {
    eventSchema,
    groupItemAttrs,
    computeSchedule = {},
    cacheComputeSchedule = {},
    modalCalendarTable = {},
    notificationSetup,
  } = configure.main;
  const ownerId = +activeRow.c_user_id || +getCurrentUserId();

  const callback = (type, data = {}) => {
    switch (type) {
      case 'UPDATE_CONDITIONS': {
        console.log('----', data);
        const dataTmp = safeParse(data.data);
        if (
          dataTmp.type === 'ADD_ITEM' ||
          dataTmp.type === 'ADD_GROUP' ||
          dataTmp.type === 'DELETE_GROUP' ||
          dataTmp.type === 'DELETE_ITEM' ||
          dataTmp.type === 'ON_CHANGE_DATERANGE'
        ) {
          props.validateScheduleDynamic();
        }
        props.callback('UPDATE_CONDITIONS', { type, data });
        break;
      }
      case 'ADD_MATCHING_FILE': {
        props.callback('ADD_MATCHING_FILE', data);
        break;
      }
      default:
        break;
    }
  };
  const renderMessageError = () => {
    const mesSettings = {};

    if (design === 'update' && props.main.activeRow.status === ARCHIVE_STATUS) {
      mesSettings.bgColor = '#e5f5f8';
      mesSettings.messageCode = TRANSLATE_KEY.KHONG_CO;
      mesSettings.defaultMessage =
        'This collection is archived, you can’t change its condition';
    } else if (
      design === 'update' &&
      props.main.activeRow.process_status == 1
    ) {
      mesSettings.bgColor = '#e5f5f8';
      mesSettings.messageCode = TRANSLATE_KEY._NOTI_SEGMENT_BEING_COMPUTED;
      mesSettings.defaultMessage =
        'This collection is being computed, you can’t change its condition until the building process finished';
    } else if (
      design === 'update' &&
      props.main.activeRow.process_status == 3
    ) {
      mesSettings.bgColor = '#feefc3';
      mesSettings.messageCode = TRANSLATE_KEY._NOTI_SEGMENT_COMPUTE_ERROR;
      mesSettings.defaultMessage =
        'Compute process of this collection got error, please try again.';
    } else if (
      design === 'update' &&
      props.main.activeRow.process_status == 5
    ) {
      mesSettings.bgColor = '#feefc3';
      mesSettings.messageCode = TRANSLATE_KEY._004;
      mesSettings.defaultMessage =
        'This Segment is in the queue waiting for computation. If you want to edit its conditions, you need to disable it before the computing starts.';
    }

    if (Object.keys({ ...mesSettings }).length > 0) {
      return (
        <DivMessageComputing bgColor={mesSettings.bgColor}>
          {getTranslateMessage(
            mesSettings.messageCode,
            mesSettings.defaultMessage,
          )}
        </DivMessageComputing>
      );
    }

    return null;
  };

  const disabled =
    design === 'update' &&
    !isVersion &&
    (activeRow.process_status == 1 || activeRow.process_status == 5);

  const disabledFooter =
    design === 'update' &&
    (activeRow.process_status == 1 || activeRow.process_status == 5) &&
    // chỉ disable khi current name/description(FE) !== prev name/description(API)
    activeRow.segment_display === configure.main.name && // compare name
    safeParse(activeRow.description, '') === configure.main.description; // compare description
  // console.log(
  //   'props.main.itemTypeId: ',
  //   isOpenDetail,
  //   props.main.itemTypeId,
  //   isOpenDetail && parseInt(props.main.itemTypeId) === -1003,
  // );
  return (
    <WrapperContent width={width}>
      {/* <StyleScrollBar id="scroll-bar"> */}
      <StyleContent
        style={
          !isEdit
            ? {
                // pointerEvents: 'none',
                opacity: '0.5',
                cursor: 'not-allowed',
              }
            : {}
        }
        isVersion={isVersion}
        // className="p-right-3 p-top-3"
      >
        <LayoutLoadingCreateSegment
          isLoading={props.isLoading}
          design={props.design}
        >
          <UIWrapperDisable disabled={isVersion}>
            <WrapperDetail isOpen={isOpenDetail}>
              {isOpenDetail && (
                <div style={closeBtnStyle}>
                  <Icon onClick={() => props.callback('ON_CLOSE')}>cancel</Icon>
                </div>
              )}
              <CustomerDetail
                isOpen={
                  isOpenDetail && parseInt(props.main.itemTypeId) === -1003
                }
                activeItemId={activeItem.id}
                // activeItemId={-100001}
                callback={props.callback}
              />
              <VisitorDetail
                isOpen={
                  isOpenDetail && parseInt(props.main.itemTypeId) === -1007
                }
                activeItemId={activeItem.id}
                callback={props.callback}
              />
            </WrapperDetail>
            <div style={{ display: isOpenDetail ? 'none' : 'block' }}>
              {!isVersion && renderMessageError()}
              <StyledWrapperDisable
                className="width-100"
                disabled={activeRow.status === ARCHIVE_STATUS}
              >
                <Name
                  callback={props.callback}
                  name={configure.main.name}
                  description={configure.main.description}
                  errors={configure.main.errors}
                  itemTypeId={props.main.itemTypeId}
                  labels={MAP_TITLE}
                  isForceHideName={props.isUIV2}
                />
                {/* )} */}
                {/* <ListMember design={props.design} disabled={disabled} /> */}
                {configure.main.type === 1 ? (
                  <Condition
                    version={props.moduleConfig.key}
                    moduleConfig={props.moduleConfig}
                    eventSchema={eventSchema}
                    dataGroupAttrs={groupItemAttrs}
                    callback={callback}
                    rules={props.rules}
                    disabled={disabled}
                    conditionOptions={props.conditionOptions}
                    labels={MAP_TITLE}
                    boxShadow={false}
                  />
                ) : (
                  <MatchingFile
                    boxShadow={false}
                    use="collection"
                    itemTypeId={props.main.itemTypeId}
                    moduleConfig={props.moduleConfig}
                    disabled={disabled}
                    labels={MAP_TITLE}
                    computeSchedule={configure.main.computeSchedule}
                  />
                )}
                <WrapperSegmentMembers>
                  <SegmentMember
                    use="collection"
                    itemTypeId={props.main.itemTypeId}
                    disabled={
                      disabled ||
                      (design === 'update' &&
                        configure.main.type === 2 &&
                        configure.matchingFile.droppedFiles.length === 0)
                    }
                    initData={configure.main.initDataSegmentMember}
                    callback={props.callback}
                    labels={MAP_TITLE}
                  />
                  {/* <CollectionItem
                    itemTypeId={props.main.itemTypeId}
                    disabled={disabled}
                    initData={configure.main.initDataSegmentMember}
                    callback={props.callback}
                  /> */}
                  <GeneralSetting
                    use="collection"
                    design={design}
                    // disabled={disabled || configure.main.type === 2}
                    disabled={disabled}
                    disabledDynamic={main.disabledDynamicSchedule}
                    initData={configure.main.initDataComputeSchedule}
                    callback={props.callback}
                    labels={MAP_TITLE}
                    computeSchedule={configure.main.computeSchedule}
                  />
                </WrapperSegmentMembers>
                {computeSchedule.type === 'dynamic' && (
                  <ComputationSchedule
                    isLoading={computeSchedule.isLoading}
                    design={design}
                    disabled={disabled}
                    labels={MAP_TITLE}
                    computeSchedule={computeSchedule}
                    callback={props.callback}
                    errors={configure.main.errors}
                  />
                )}
                <NotificationSetup
                  value={notificationSetup}
                  onChange={props.onChangeNotificationSetup}
                  ownerId={ownerId}
                  boxShadow={false}
                />
                {!isVersion && (
                  <Footer
                    use="collection"
                    callback={props.callback}
                    disabled={main.disabled || !isEdit}
                    activeRow={activeRow}
                    computeSchedule={computeSchedule}
                    cacheComputeSchedule={cacheComputeSchedule}
                    isValidTotalLimit={isValidTotalLimit}
                    isDoing={main.isDoing}
                    design={props.design}
                    disabledFooter={disabledFooter}
                    modalCalendarTable={modalCalendarTable}
                    onChangeErrors={props.onChangeErrors}
                    isUIV2={props.isUIV2}
                    // activeRowCurrentVersion={props.activeRowCurrentVersion}
                  />
                )}
              </StyledWrapperDisable>
            </div>
          </UIWrapperDisable>
        </LayoutLoadingCreateSegment>
      </StyleContent>

      {/* </StyleScrollBar> */}
    </WrapperContent>
  );
}

const mapStateToProps = createStructuredSelector({
  rules: makeSelectCreateSegmentCondition(),
  main: makeSelectMainCreateSegment(),
  configure: makeSelectConfigCreateSegment(),
  // activeRowCurrentVersion: makeSelectActiveRowCurrentVersion(),
});

function mapDispatchToProps(dispatch, props) {
  return {
    validateScheduleDynamic: payload =>
      dispatch(
        updateValue(
          `${props.moduleConfig.key}@@VALIDATE_SCHEDULE_TYPE_DYNAMIC@@`,
          payload,
        ),
      ),
    onChangeNotificationSetup: payload =>
      dispatch(
        updateValue(`${props.moduleConfig.key}@@NOTIFICATION_SETUP@@`, payload),
      ),
    onChangeErrors: value =>
      dispatch(updateValue(`${props.moduleConfig.key}@@ERRORS`, value)),
  };
}

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(Content);
