/* eslint-disable indent */
/* eslint-disable react/prop-types */
import React, { useState, useEffect, useCallback, memo, useMemo } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter, useParams, useHistory } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import useNotificationBar from 'hooks/useNotificationBar';
import ErrorBoundary from 'components/common/ErrorBoundary';
import NavBar from 'components/Organisms/NavBar/index';
import TableContainer from 'containers/Table';
import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';
import {
  UIIconButton as IconButton,
  UILoading as Loading,
  UIDrawer,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';

import UIHelmet from 'components/Templates/LayoutContent/Helmet';

import CloseIcon from '@material-ui/icons/Close';
import HistoryIcon from '@material-ui/icons//History';
import ModalHeader from 'components/Molecules/ModalHeader/index';
import queryString from 'query-string';
import ObjectServices from 'services/Object';
import reducer from './reducer';
import saga from './saga';
import {
  makeSelectListAttributeTable,
  makeSelectListAttributeFilter,
  makeSelectListAttributeColumn,
  makeSelectListAttributeMain,
  makeSelectListAttributeData,
  makeSelectListAttributeMapDataFooter,
  makeSelectListAttributeDateRange,
} from './selectors';
import ControlTable from './ControlTable';
import BOAttributeServices from '../../../../../../../services/DataView';
// import EventSourcesServices from '../../../../../../../services/EventSources';
import { MODULE_CONFIG } from './config';
import { MODULE_CONFIG as MODULE_CONFIG_DETAIL } from '../config';
import Filters from '../../../../../../../containers/Filters';
import ModifyColumn from '../../../../../../../containers/ModifyColumn';

import {
  init,
  getList,
  reset,
  addNotification,
  update,
  updateValue,
} from '../../../../../../../redux/actions';
import { WrapActionTable } from '../../../../../../../containers/Table/styles';
import {
  TableWrapper,
  TableRelative,
  WapperStyle,
  WapperStyleHeader,
  WapperIcon,
  WrapperMessageWarning,
} from './styles';
import { getTranslateMessage } from '../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../messages/constant';
import Search from '../../../../../../../containers/Search';
import LayoutContent, {
  LayoutContentLoading,
  StyledWrapperLoading,
} from '../../../../../../../components/Templates/LayoutContent';
import { safeParse } from '../../../../../../../utils/common';
import ModalDeleteMulti from '../../../../../../../containers/modals/ModalDeleteMultiType2';
import NoDataComponent from '../../../../../../../components/common/NoDataFound';
import AddComponent from './AddComponent';
import {
  mapParamsDeleteFn,
  listTitleAcceptedFn,
  getTitleDeleteWarning,
} from './utils';
import {
  makeSelectDetailActiveRow,
  makeSelectDetailDomainMain,
} from '../../selectors';
import useToggle from '../../../../../../../hooks/useToggle';
import ModalAssign from './ModalAssign';
import { getMessageByCode } from '../../../../../../../services/utils';
import APP from '../../../../../../../appConfig';
import { getPortalId } from '../../../../../../../utils/web/cookie';
import {
  ContainerCustomize,
  WrapperContentCustomize,
  WrapperDrawer,
} from '../../../../../MarketingHub/Journey/Create/Content/Nodes/Destination/Testing/styles';
import ComputaitionList from '../ComputationHistories';
import ModalArchive from '../../../../../../../containers/modals/ModalArchive';
import ModalRecover from '../../../../../../../containers/modals/ModalRecover';
import ModalDowload from '../../../../../../../containers/modals/ModalDownloadBO';
import UploadV2 from '../../../../../../../containers/UploadV2';
import ModalForceRun from '../../../../../../../containers/modals/ModalForceRun';
import { ModalListEventSource } from '../../../../../../../containers/modals/ModalListEventSource';
import {
  serializeLabelToCodeAttr,
  trackEvent,
} from '../../../../../../../utils/web/utils';
import ModalRebuild from '../../../../../../../containers/modals/ModalRebuild';
import ModalConfirm from '../../../../../../../containers/modals/ModalConfirm';
import { getParentRoute } from '../../../utils';
import { isShowAction } from '../../../../BusinessObject/utils';
import { makeSelectSourceActive } from '../../../selectors';

const PREFIX = MODULE_CONFIG.key;
const MAP_TITLE = {
  title: getTranslateMessage(TRANSLATE_KEY._TITL_ATTRIBUTE, 'Attribute'),
  itemNameDest: getTranslateMessage(TRANSLATE_KEY._ITEM_NAME_, 'events'),
  actDownload: getTranslateMessage(TRANSLATE_KEY._ACT_DOWNLOAD, 'DOWNLOAD'),
  actExport: getTranslateMessage(TRANSLATE_KEY._ACT_EXPORT, 'EXPORT'),
  labelArchive: getTranslateMessage(
    TRANSLATE_KEY._,
    'By default, the data with Archived status are hidden from the Datagrid',
  ),
  actMore: getTranslateMessage(TRANSLATE_KEY._ACT_MORE, 'MORE'),
  guideNoDestYet: getTranslateMessage(
    TRANSLATE_KEY._USER_GUIDE_NO_ATTRIBUTE,
    "You haven't created any Attribute yet",
  ),
  table: getTranslateMessage(TRANSLATE_KEY._, 'HISTORY'),
  titleComp: getTranslateMessage(
    TRANSLATE_KEY._TITL_ATTR_COMP_HISTORY,
    'Attributes Computation Histories',
  ),
  messageLimitWarningRebuild: getTranslateMessage(
    TRANSLATE_KEY._WARN_ATTR_COMPUTATIONAL_LIMIT,
    'Currently, the computation limit has been reached, the system will compute this attribute in the next valid time.',
  ),
};
// const layoutStyle = {
//   overflow: 'hidden',
//   height: 'calc(100vh - 108px)',
// };

// const defaultMetric = ['impression', 'click'];

export function DataTableListting(props) {
  const {
    main,
    table,
    filter,
    column,
    dateRange,
    itemTypeId,
    activeTab,
    typeDataView,
    objectActive,
    mainDetail,
  } = props;
  const { isShow } = useNotificationBar();
  const [isOpenModalDelete, setIsOpenModalDelete] = useState(false);
  const [selectedRowCellStatus, setSelectedRowsCellStatus] = useState(
    new Map(),
  );
  const [isOpenModalAssign, toggleModalAssign] = useToggle(false);
  const [isOpenModalArchive, toggleModalArchive] = useToggle(false);
  const [isOpenModalRecover, toggleModalRecover] = useToggle(false);
  const [isOpenModalEvent, toggleModalEvent] = useToggle(false);
  // const [isOpenModalDownload, toggleModalDownload] = useToggle(false);
  const [isOpenModalDownload, toggleModalDownload] = useToggle(false);
  const { isInitDone, moduleConfig, moduleConfigColumn, computeId } = main;
  const history = useHistory();
  const [isOpenModalComp, toggleModalComp] = useToggle(false);
  const [isOpenModalForceRun, toggleModalForceRun] = useToggle(false);
  const isShowActionAttr = isShowAction(typeDataView, objectActive);
  const { checkPermision } = mainDetail;
  const { isEdit = true, isView = true } = checkPermision;
  const { itemTypeIdBO } = props.activeRow;
  // const { itemTypeId = 'default' } = useParams();
  useEffect(() => {
    if (activeTab === 'attributes') {
      const object = {
        type: 'attribute',
        name: 'attribute',
      };
      trackEvent('object', 'listing', object);
    }

    const { computeId, itemPropertyName } = queryString.parse(
      window.location.search,
    );
    if (computeId) {
      toggleModalComp();
    }
    props.init({
      isShowActionAttr: isEdit,
      itemTypeId,
      itemPropertyName,
      itemTypeIdBO,
    });
    return () => {
      props.reset();
    };
  }, [itemTypeId]);

  const callback = (type, dataIn) => {
    // console.log('type, dataIn', type, dataIn);
    //
    switch (type) {
      case 'FETCH_DATA': {
        props.fetchData();
        break;
      }
      case 'ACTION_TABLE_DELETE': {
        setIsOpenModalDelete(true);
        break;
      }
      case 'ACTION_TABLE_ASSIGN': {
        toggleModalAssign();
        break;
      }
      case 'OPEN_TAB_CREATE_COPY': {
        window.open(
          getParentRoute(
            `/detail/${itemTypeId}/attributes/create?createCopy=${dataIn.id}`,
          ),
          // `${
          //   APP.PREFIX
          // }/${getPortalId()}/api-hub/data-view/detail/${itemTypeId}/attributes/create?createCopy=${
          //   dataIn.id
          // }`,
          '_blank',
        );

        break;
      }
      case 'UPDATE_TOGGLE_CELL': {
        if (
          dataIn.status &&
          (dataIn.row.type !== 2 &&
            dataIn.row.compute_type !== 'custom_function')
        ) {
          props.onToggleData(dataIn);
          toggleModalForceRun();
        } else {
          props.onChangeStatus(dataIn);
        }
        break;
      }
      case 'ACTION_TABLE_DISABLED_ENABLED': {
        let isShowModalForceRunTemp = false;

        if (dataIn && dataIn.selectedRows && dataIn.selectedRows.size === 1) {
          dataIn.selectedRows.forEach((item, key) => {
            if (item.type === 3) {
              isShowModalForceRunTemp = true;
            }
          });
        }

        if (
          (dataIn.status === 1 && dataIn.isType) ||
          (dataIn.status === 1 && isShowModalForceRunTemp)
        ) {
          toggleModalForceRun();
        } else {
          props.onChangeActionTable(dataIn);
        }
        break;
      }
      case 'ACTION_TABLE_ARCHIVE': {
        // setSelectedRows(data);
        toggleModalArchive();
        break;
      }
      case 'ACTION_TABLE_RECOVER': {
        // setSelectedRows(data);
        if (dataIn.selectedRows && dataIn.selectedRows.size === 1) {
          setSelectedRowsCellStatus(dataIn.selectedRows);
        }

        toggleModalRecover();
        break;
      }
      case 'IS_OPEN_MODAL_LIST_EVENT': {
        toggleModalEvent();
        break;
      }
      case `ACTION_TABLE_FORCE_RUN`: {
        if (dataIn.isToggle) {
          props.onChangeStatus(dataIn);
        } else {
          props.onChangeActionTable(dataIn);
        }
        props.updateToggleData();

        // props.onChangeActionTable(data);
        break;
      }
      // case 'TOGGLE_MODAL_DOWNLOAD': {
      //   toggleModalDownload();
      //   break;
      // }
      case 'SEARCH_GOTO': {
        props.onSearchGoToDetails(dataIn);
        break;
      }
      case 'ACTION_TABLE_CLONE': {
        const id = dataIn.keys().next().value;
        console.log('dataIn', id, dataIn);
        history.push(
          getParentRoute(
            `/detail/${itemTypeId}/attributes/create?createCopy=${id}`,
          ),
          // `${
          //   APP.PREFIX
          // }/${getPortalId()}/api-hub/data-view/detail/${itemTypeId}/attributes/create?createCopy=${id}`,
        );
        break;
      }
      case 'ACTION_REBUILD': {
        console.log('rebuild', { type, dataIn });
        if (dataIn.status === 1) {
          toggleModalRebuild();
        } else {
          props.onRebuildAtribute(dataIn);
        }
      }
      default: {
        props.callback(type, dataIn);
        break;
      }
    }
  };

  // console.log('moduleConfig', moduleConfig);

  const tableColumns = React.useMemo(() => column.tableColumns, [
    column.tableColumns,
  ]);
  const onClickOpenCompHistories = () => {
    toggleModalComp();
  };
  return (
    <ErrorBoundary path="app/modules/Dashboard/Settings/DataView/Detail/Attributes/List/index.jsx">
      {isInitDone && Object.keys(checkPermision).length > 0 ? (
        <>
          <LayoutContent
            padding="0px 15px 15px 15px"
            // margin="0px -4px -4px -4px"
            // className="m-top-2"
            overflow="hidden"
            height={isShow ? 'calc(100vh - 55px)' : 'calc(100vh - 55px)'}
          >
            <LayoutContentLoading isLoading={!isInitDone}>
              <TableRelative>
                <TableWrapper>
                  <TableContainer
                    columnActive={column.columnObj}
                    table={table}
                    isLoading={main.isLoading}
                    moduleConfig={moduleConfig}
                    selectedIds={table.selectedIds}
                    selectedRows={table.selectedRows}
                    isSelectedAll={table.isSelectedAll}
                    isSelectedAllPage={table.isSelectedAllPage}
                    noCheckboxAction //= {!isEdit}
                    columns={tableColumns}
                    labelArchive={MAP_TITLE.labelArchive}
                    data={props.data}
                    global={{
                      isUseCallback: true,
                      statusTooltip: getTranslateMessage(
                        TRANSLATE_KEY._NOTI_ATTR_HAS_BEEN_REMOVE,
                        'The attribute has been removed',
                      ),
                    }}
                    callback={callback}
                    resizeColName="item_property_display"
                    widthFirstColumns={168} //  48 + 120
                    initialWidthColumns={448} //  280 + 48
                    ComponentControlTable={ControlTable}
                    NoDataComponent={() => (
                      <NoDataComponent
                        // AddComponent={
                        //   isEdit
                        //     ? () => (
                        //         <AddComponent
                        //           className="m-left-0"
                        //           itemTypeId={itemTypeId}
                        //           itemTypeIdBO={itemTypeIdBO}
                        //         />
                        //       )
                        //     : () => <></>
                        // }
                        guideNoYet={MAP_TITLE.guideNoDestYet}
                      />
                    )}
                    // isShowFooter
                    mapDataFooter={props.mapDataFooter}
                  >
                    <>
                      <Filters
                        use="list"
                        rules={filter.rules}
                        moduleConfig={moduleConfig}
                        filterActive={filter.config.filterObj}
                        filterCustom={filter.config.library.filterCustom}
                        libraryFilters={filter.config.library.filters}
                        //
                        groups={main.groupAttributes.groupsFilter}
                        isFilter={filter.config.design.isFilter}
                        isLoading={filter.config.isLoading}
                        // AddComponent={
                        //   isEdit
                        //     ? () => (
                        //         <AddComponent
                        //           callback={callback}
                        //           itemTypeId={itemTypeId}
                        //           itemTypeIdBO={itemTypeIdBO}
                        //         />
                        //       )
                        //     : () => <></>
                        // }
                        addComponentProps={{ itemTypeId }}
                      />
                      <WrapActionTable
                        show={!filter.config.design.isFilter}
                        className="p-x-4"
                      >
                        <div className="actionTable__inner">
                          {/* <IconButton
                            iconName="history"
                            size="24px"
                            onClick={onClickOpenCompHistories}
                            isVertical
                          >
                            {MAP_TITLE.table.toUpperCase()}
                          </IconButton> */}
                          {/* <IconButton
                            iconName="table"
                            size="24px"
                            onClick={onClickOpenCompHistories}
                            isVertical
                            // disabled
                          >
                            {MAP_TITLE.table.toUpperCase()}
                          </IconButton> */}
                          <WrapperDisable>
                            <Search
                              moduleConfig={moduleConfig}
                              suggestionType="suggestionMultilang"
                              moduleLabel={MAP_TITLE.title}
                              config={{
                                limit: 20,
                                page: 1,
                                search: '',
                                objectType: 'BO_ATTRIBUTE',
                                sort: 'asc',
                                filters: {
                                  OR: [
                                    {
                                      AND: [
                                        {
                                          column: 'item_type_id',
                                          operator: 'equals',
                                          data_type: 'number',
                                          value: itemTypeIdBO,
                                        },
                                      ],
                                    },
                                  ],
                                },
                              }}
                              callback={callback}
                            />
                          </WrapperDisable>
                          <ModifyColumn
                            sort={table.sort}
                            moduleConfig={moduleConfigColumn}
                            columnActive={column.columnObj}
                            columnCustom={column.library.columnCustom}
                            libraryColumns={column.library.columns}
                            requires={main.groupAttributes.requires}
                            defaults={main.groupAttributes.defaults}
                            defaultSortColumns={
                              moduleConfigColumn.defaultSortColumns
                            }
                            columns={column.columnObj.columns.columnsAlias}
                            groups={main.groupAttributes.groups}
                            isLoading={column.isLoading}
                          />
                          {/* {isEdit && <UploadV2 viewId={itemTypeId} />} */}
                        </div>
                      </WrapActionTable>
                    </>
                  </TableContainer>
                </TableWrapper>
              </TableRelative>
            </LayoutContentLoading>
          </LayoutContent>
          <ModalDowload
            isOpen={isOpenModalDownload}
            toggle={toggleModalDownload}
            paging={table.paging}
            objectName={serializeLabelToCodeAttr(props.activeRow.viewDisplay)}
            object_type={MODULE_CONFIG.objectType}
            ObjectServicesFn={
              BOAttributeServices.BOAttribute.dowloadBOAttribute
            }
            // decryptFields={getItemAttributeDecryptFields(itemTypeId)}
            sort={table.sort}
            sortDefault="last_updated_date"
            filters={filter}
            itemTypeId={itemTypeIdBO}
            itemTypeName="user"
            columns={column.columnObj.columns.columnsAlias}
          />
          <ModalForceRun
            activeRows={
              table.selectedRows && table.selectedRows.size === 0
                ? selectedRowCellStatus
                : table.selectedRows
            }
            isSelectedAll={table.isSelectedAll}
            totalRecord={table.paging.totalRecord}
            rules={filter.rules}
            label={getTranslateMessage(TRANSLATE_KEY._, 'Attribute')}
            placeHolderName=""
            callback={callback}
            dataToggle={main.dataToggle}
            isToggle={main.isToggle}
            moduleName="Action success"
            isOpen={isOpenModalForceRun}
            toggle={toggleModalForceRun}
          />
          <ModalArchive
            code="attribute"
            activeRows={table.selectedRows}
            isSelectedAll={table.isSelectedAll}
            totalRecord={table.paging.totalRecord}
            rules={filter.rules}
            label={getTranslateMessage(TRANSLATE_KEY._, 'attribute')}
            placeHolderName=""
            callback={callback}
            // moduleName={MAP_TITLE.itemNameSegment}
            ObjectServicesFn={BOAttributeServices.BOAttribute.archiveBOAttr}
            isOpen={isOpenModalArchive}
            toggle={toggleModalArchive}
            itemTypeId={itemTypeId}
          />
          <ModalRecover
            activeRows={
              table.selectedRows && table.selectedRows.size === 0
                ? selectedRowCellStatus
                : table.selectedRows
            }
            isSelectedAll={table.isSelectedAll}
            totalRecord={table.paging.totalRecord}
            rules={filter.rules}
            label={getTranslateMessage(TRANSLATE_KEY._, 'Attributes')}
            placeHolderName=""
            callback={callback}
            // moduleName={MAP_TITLE.itemNameSegment}
            isOpen={isOpenModalRecover}
            toggle={toggleModalRecover}
          />

          {/* Warning rebuiled */}
          {main.isLimitAttribute && (
            <ModalConfirm
              isOpen={main.isLimitAttribute}
              isFooterV2
              toggle={() => {
                props.toggleModalWarningLimitations({
                  isOpen: false,
                });
              }}
              title={getTranslateMessage(
                TRANSLATE_KEY._WARN_TITLE_ATTR_WARN_TOTAL,
                'Warning Total Attribute Limit',
              )}
            >
              <WrapperMessageWarning>
                {MAP_TITLE.messageLimitWarningRebuild}
              </WrapperMessageWarning>
            </ModalConfirm>
          )}

          {/* Warning recovered */}
          <ModalConfirm
            isFooterV2
            title={getTranslateMessage(
              TRANSLATE_KEY._WARN_TITLE_ATTR_WARN_TOTAL,
              'Warning Total Attribute Limit',
            )}
            isOpen={main.modalWarningRecoverLimited.isOpen}
            toggle={props.closeModalWarningRecoverLimited}
          >
            {main.modalWarningRecoverLimited.warningMes}
          </ModalConfirm>

          {/* Warning computational limited */}
          <ModalConfirm
            isFooterV2
            title={getTranslateMessage(
              TRANSLATE_KEY._WARN_TITLE_ATTR_COMPUTATIONAL_LIMIT,
              'Warning Attribute Computational Limit',
            )}
            isOpen={main.modalComputationLimited.isOpen}
            toggle={props.closeModalComputationLimited}
          >
            {main.modalComputationLimited.warningMes}
          </ModalConfirm>

          {isOpenModalDelete && (
            <ModalDeleteMulti
              isUseAPICheck={false}
              extendParams={{ itemTypeId }}
              // isUseAPICheck
              activeRows={table.selectedRows}
              isSelectedAll={table.isSelectedAll}
              totalRecord={table.paging.totalRecord}
              rules={filter.rules}
              label={getTranslateMessage(
                TRANSLATE_KEY._BOX_TITL_REMOVE_EVENT_ATTRIBUTE,
                'Remove Events',
              )}
              placeHolderName=""
              moduleName={MAP_TITLE.title}
              ObjectServicesFn={BOAttributeServices.BOAttribute.updateStatus}
              listTitleAcceptedFn={listTitleAcceptedFn}
              isOpenModal={isOpenModalDelete}
              setOpenModal={setIsOpenModalDelete}
              fetchData={props.fetchData}
              mapParamsFn={mapParamsDeleteFn}
              getTitleWarning={getTitleDeleteWarning}
            />
          )}
          <ModalListEventSource
            isOpen={isOpenModalEvent}
            toggle={toggleModalEvent}
            callback={callback}
            dataParams={{
              objectType: 'BO_ATTRIBUTE',
              filters: {
                OR: [
                  {
                    AND: [
                      {
                        column: 'item_type_id',
                        operator: 'equals',
                        data_type: 'number',
                        value: itemTypeId,
                      },
                    ],
                  },
                ],
              },
              sort: 'asc',
            }}
            label="Select Attribute"
            ObjectServicesFn={ObjectServices.suggestionMultilang.getList}
          />
          <UIDrawer isOpen={isOpenModalComp} toggle={toggleModalComp}>
            <WrapperDrawer width={window.innerWidth > 1600 ? '1256px' : '80vw'}>
              <WapperStyleHeader>
                <div className="text">{MAP_TITLE.titleComp}</div>
                <div>
                  <IconButton onClick={toggleModalComp}>
                    <CloseIcon />
                  </IconButton>
                </div>
              </WapperStyleHeader>
              <ContainerCustomize>
                <WrapperContentCustomize>
                  <ComputaitionList
                    isEdit={isEdit}
                    activeTab={main.activeTab}
                    itemTypeId={itemTypeId}
                    itemTypeIdBO={itemTypeIdBO}
                  />
                </WrapperContentCustomize>
              </ContainerCustomize>
            </WrapperDrawer>
          </UIDrawer>
          <ModalAssign
            isOpen={isOpenModalAssign}
            toggle={toggleModalAssign}
            itemTypeId={itemTypeId}
            selectedRows={table.selectedRows}
            fetchData={props.fetchData}
            callback={callback}
          />
        </>
      ) : (
        <StyledWrapperLoading style={{ height: 'calc(100vh - 132px)' }}>
          <Loading isLoading={!isInitDone} />
        </StyledWrapperLoading>
      )}
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  mainDetail: makeSelectDetailDomainMain(),
  main: makeSelectListAttributeMain(),
  table: makeSelectListAttributeTable(),
  filter: makeSelectListAttributeFilter(),
  column: makeSelectListAttributeColumn(),
  data: makeSelectListAttributeData(),
  mapDataFooter: makeSelectListAttributeMapDataFooter(),
  dateRange: makeSelectListAttributeDateRange(),
  activeRow: makeSelectDetailActiveRow(),
  objectActive: makeSelectSourceActive(),
});

function mapDispatchToProps(dispatch) {
  return {
    init: params => {
      dispatch(init(PREFIX, params));
    },
    reset: params => {
      dispatch(reset(PREFIX, params));
    },
    fetchData: params => {
      dispatch(getList(PREFIX, params));
    },
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
    onChangeStatus: params => {
      dispatch(update(`${PREFIX}@@CELL_STATUS`, params));
    },
    onChangeDateRange: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@DATE_RANGE`, params));
    },
    toggleModalEventAttribute: () => {
      dispatch(
        updateValue(`${MODULE_CONFIG_DETAIL.key}@@MODAL_EVENT_ATTRIBUTE@@`),
      );
    },
    onSearchGoToDetails: params => {
      dispatch(updateValue(`${PREFIX}@@COMMON_FILTER_SEARCH_GOTO`, params));
    },
    onChangeActionTable: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@ACTION_STATUS`, params));
    },
    onToggleData: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@TOGGLE_DATA`, params));
    },
    updateToggleData: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@TOGGLE_DATA_UPDATE`, params));
    },
    onRebuildAtribute: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@ACTION_REBUILD`, params));
    },
    toggleModalWarningLimitations: data => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@IS_LIMIT_REBUILD_ATTR@@`, data),
      );
    },
    closeModalComputationLimited: () => {
      dispatch(
        updateValue(
          `${MODULE_CONFIG.key}@@TOGGLE_MODAL_LIMIT_WARNING_WHEN_COMPUTED`,
          { isOpen: false },
        ),
      );
    },
    closeModalWarningRecoverLimited: () => {
      dispatch(
        updateValue(
          `${MODULE_CONFIG.key}@@TOGGLE_MODAL_LIMIT_WARNING_WHEN_RECOVERED`,
          {
            isOpen: false,
          },
        ),
      );
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

const withReducer = injectReducer({ key: PREFIX, reducer });
const withSaga = injectSaga({ key: PREFIX, saga });

export default compose(
  withRouter,
  withReducer,
  withSaga,
  withConnect,
)(DataTableListting);
