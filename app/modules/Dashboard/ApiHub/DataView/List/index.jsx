/* eslint-disable react/prop-types */
import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { useLocation, withRouter } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import ErrorBoundary from 'components/common/ErrorBoundary';
import NavBar from 'components/Organisms/NavBar/index';
import TableContainer from 'containers/Table';
import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';
import {
  UIIconButton as IconButton,
  UILoading as Loading,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';

import UIHelmet from 'components/Templates/LayoutContent/Helmet';
import queryString from 'query-string';

import AddComponent from 'containers/AddComponent';
import useNotificationBar from 'hooks/useNotificationBar';
import reducer from './reducer';
import saga from './saga';
import {
  makeSelectListObjectTable,
  makeSelectListObjectFilter,
  makeSelectListObjectColumn,
  makeSelectListObjectMain,
  makeSelectListObjectData,
  makeSelectListObjectMapDataFooter,
  makeSelectListObjectDateRange,
  makeSelectIsLimitationPortal,
} from './selectors';
import ControlTable from './ControlTable';
import BusinessObjectServices from '../../../../../services/BusinessObject';
import { useLocalStorage } from '../../../../../utils/web/useHooks';
import { MODULE_CONFIG } from './config';
import {
  MODULE_CONFIG as MODULE_CONFIG_DETAIL,
  BREADCRUMDATA,
} from '../config';
import Filters from '../../../../../containers/Filters';
import ModifyColumn from '../../../../../containers/ModifyColumn';
// Hooks

import {
  init,
  getList,
  reset,
  addNotification,
  update,
  updateValue,
} from '../../../../../redux/actions';
import { WrapActionTable } from '../../../../../containers/Table/styles';
import { TableWrapper, TableRelative } from './styles';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import Search from '../../../../../containers/Search';
import LayoutContent, {
  LayoutContentLoading,
  StyledWrapperLoading,
} from '../../../../../components/Templates/LayoutContent';
import ModalDeleteMulti from '../../../../../containers/modals/ModalDeleteMultiType2';
import NoDataComponent from '../../../../../components/common/NoDataFound';

import {
  mapParamsDeleteFn,
  listTitleAcceptedFn,
  getTitleDeleteWarning,
} from './utils';
import CustomHeader from '../../../../../components/Organisms/CustomHeader';
import { getMessageByCode } from '../../../../../services/utils';
import ModalArchive from '../../../../../containers/modals/ModalArchive';
import ModalRecover from '../../../../../containers/modals/ModalRecover';
import useToggle from '../../../../../hooks/useToggle';
import ModalDowload from '../../../../../containers/modals/ModalDownloadBO';
import UploadV2 from '../../../../../containers/UploadV2';
import { safeParse, trackEvent } from '../../../../../utils/web/utils';
import ModalConfirm from '../../../../../containers/modals/ModalConfirm';
import { MENU_CODE } from '../../../menu.config';
import DrawerCreateDataView from '../Create/Loadable';
import DrawerDetailDataView from '../Detail/index';
import { checkPermissionViewEdit } from '../../../../../utils/web/permission';
import { makeSelectDashboardModalSelectAccount } from '../../../selector';

const PREFIX = MODULE_CONFIG.key;
const MAP_TITLE = {
  title: getTranslateMessage(TRANSLATE_KEY._MENU_SUB_DATA_VIEW, 'Data Views'),
  labelArchive: getTranslateMessage(
    TRANSLATE_KEY._,
    'By default, the data with Archived status are hidden from the Datagrid',
  ),
  lableBO: getTranslateMessage(TRANSLATE_KEY._TITL_OBJECTS, 'Objects'),
  itemNameDest: getTranslateMessage(TRANSLATE_KEY._, 'business objects'),
  actDownload: getTranslateMessage(TRANSLATE_KEY._ACT_DOWNLOAD, 'DOWNLOAD'),
  actExport: getTranslateMessage(TRANSLATE_KEY._ACT_EXPORT, 'EXPORT'),
  actMore: getTranslateMessage(TRANSLATE_KEY._ACT_MORE, 'MORE'),
  guideNoDestYet: getTranslateMessage(
    TRANSLATE_KEY._USER_GUIDE_NO_OBJECT,
    "You haven't created any Object yet",
  ),
  guideNoDestYetShared: getTranslateMessage(
    TRANSLATE_KEY._USER_GUIDE_NO_SHARED_OBJECT,
    "You haven't been shared any Object",
  ),
  titleWarningLimitation: getTranslateMessage(
    TRANSLATE_KEY._WARN_TITLE_BO_WARN_TOTAL,
    'Warning Total Business Object Limit',
  ),
  contentLimitation: getTranslateMessage(
    TRANSLATE_KEY._WARN_BO_WARN_TOTAL_CONTENT_CREATE,
    `The number of business object has reached the limit.
If you want to create new business object, please archived unused business object.
  `,
  ),
};
const layoutStyle = {
  overflow: 'hidden',
  height: 'calc(100vh - 108px)',
  margin: '-16px',
  padding: '0px 16px 16px',
};

// const defaultMetric = ['impression', 'click'];

export function GroupsListting(props) {
  const { main, table, filter, column, activeTab } = props;
  const [selectedRowCellStatus, setSelectedRowsCellStatus] = useState(
    new Map(),
  );
  const location = useLocation();
  const searchParams = new URLSearchParams(location?.search);
  const type = searchParams.get('type') || null;
  const tab = searchParams.get('tab') || 'settings';
  const itemTypeId = searchParams.get('viewId') || null;
  const { isShow: isShowExplore } = useNotificationBar();
  const [isOpenModalDelete, setIsOpenModalDelete] = useState(false);
  const [isOpenModalArchive, toggleModalArchive] = useToggle(false);
  const [isOpenModalRecover, toggleModalRecover] = useToggle(false);
  const [isOpenModalDownload, toggleModalDownload] = useToggle(false);
  const [isOpenModalBO, toggleModalBO] = useToggle(false);
  const { isInitDone } = main;
  const [accountSelect, setAccount] = useState(null);
  const { isEditByUser, isEditEverything } = checkPermissionViewEdit(
    MENU_CODE.DATA_VIEW,
  );
  useEffect(() => {
    props.init({ activeTab });
    return () => {
      props.reset();
    };
  }, [activeTab]);

  // useEffect(() => {
  //   if (!isOpenModalLimit && props.isLimitationPortal) {
  //     props.onUpdateIsLimitation({ isLimitation: false });
  //   }
  // }, [isOpenModalLimit]);

  const callback = (type, dataIn) => {
    switch (type) {
      case 'FETCH_DATA': {
        props.fetchData();
        break;
      }
      case 'CREATE_SUCCESS': {
        toggleModalBO();
        props.fetchData();
        break;
      }
      case 'ACTION_TABLE_DELETE': {
        setIsOpenModalDelete(true);
        break;
      }
      case 'UPDATE_TOGGLE_CELL': {
        props.onChangeStatus(dataIn);
        break;
      }
      case 'ACTION_TABLE_DISABLED_ENABLED': {
        props.onChangeActionTable(dataIn);
        break;
      }
      case 'ACTION_TABLE_ARCHIVE': {
        // setSelectedRows(data);
        toggleModalArchive();
        break;
      }
      case 'ACTION_TABLE_RECOVER': {
        // setSelectedRows(data);
        if (dataIn.selectedRows && dataIn.selectedRows.size === 1) {
          setSelectedRowsCellStatus(dataIn.selectedRows);
        }
        toggleModalRecover();
        break;
      }
      case 'CREATE_WITH_PERMISSION': {
        if (dataIn.type === 'MODAL_ACCOUNT') {
          setAccount(dataIn.ownerData);
          toggleModalBO();
        } else {
          setAccount(null);
          toggleModalBO();
        }
        break;
      }
      default: {
        // props.callback(type, dataIn);
        break;
      }
    }
  };

  const tableColumns = React.useMemo(() => column.tableColumns, [
    column.tableColumns,
  ]);

  return (
    <ErrorBoundary path="app/modules/Dashboard/Settings/BusinessObject/List/index.js">
      <UIHelmet title={MAP_TITLE.title} />
      <LayoutContent
        height={`calc(100vh - ${isShowExplore ? '210px' : '150px'})`}
      >
        {/* <NavBar label={MAP_TITLE.lableBO} /> */}
        {/* <LayoutContentLoading
          classNameLoading="m-top-2"
          isLoading={!isInitDone}
        > */}
        <TableRelative>
          <TableWrapper>
            <TableContainer
              columnActive={column.columnObj}
              table={table}
              isLoading={main.isLoading}
              moduleConfig={MODULE_CONFIG}
              selectedIds={table.selectedIds}
              selectedRows={table.selectedRows}
              isSelectedAll={table.isSelectedAll}
              isSelectedAllPage={table.isSelectedAllPage}
              columns={tableColumns}
              data={props.data}
              noCheckboxAction
              labelArchive={MAP_TITLE.labelArchive}
              global={{
                isUseCallback: true,
                statusTooltip: getTranslateMessage(
                  TRANSLATE_KEY._NOTI_OBJECT_HAS_BEEN_REMOVE,
                  'The object has been removed',
                ),
                disabledCheckBoxAll: !isEditEverything,
              }}
              callback={callback}
              NoDataComponent={() => (
                <NoDataComponent
                  AddComponent={
                    activeTab === 'owner' && (isEditByUser || isEditEverything)
                      ? () => (
                          <AddComponent
                            menuCode={MENU_CODE.DATA_VIEW}
                            callback={callback}
                          />
                        )
                      : () => <></>
                  }
                  guideNoYet={
                    activeTab === 'owner'
                      ? MAP_TITLE.guideNoDestYet
                      : MAP_TITLE.guideNoDestYetShared
                  }
                />
              )}
              resizeColName="view_display"
              widthFirstColumns={168} //  48 + 120
              initialWidthColumns={448} //  280 + 48
              ComponentControlTable={ControlTable}
            >
              <>
                <Filters
                  use="list"
                  rules={filter.rules}
                  moduleConfig={MODULE_CONFIG}
                  filterActive={filter.config.filterObj}
                  filterCustom={filter.config.library.filterCustom}
                  libraryFilters={filter.config.library.filters}
                  groups={main.groupAttributes.groupsFilter}
                  isFilter={filter.config.design.isFilter}
                  isLoading={filter.config.isLoading}
                  isEdit={
                    activeTab === 'owner' && (isEditByUser || isEditEverything)
                  }
                  AddComponent={
                    activeTab === 'owner' && (isEditByUser || isEditEverything)
                      ? () => (
                          <AddComponent
                            menuCode={MENU_CODE.DATA_VIEW}
                            callback={callback}
                          />
                        )
                      : () => <></>
                  }
                />
                <WrapActionTable
                  show={!filter.config.design.isFilter}
                  className="p-x-4"
                >
                  <div className="actionTable__inner">
                    <Search
                      moduleConfig={MODULE_CONFIG}
                      suggestionType="suggestionMultilang"
                      moduleLabel={MAP_TITLE.title}
                      config={{
                        limit: 20,
                        page: 1,
                        search: '',
                        objectType:
                          activeTab === 'owner'
                            ? 'DATA_VIEW'
                            : 'DATA_VIEW_SHARED',
                        sort: 'asc',
                        filters: {
                          OR: [
                            {
                              AND: [],
                            },
                          ],
                        },
                      }}
                      isAddFilter
                    />
                    <ModifyColumn
                      sort={table.sort}
                      moduleConfig={MODULE_CONFIG}
                      columnActive={column.columnObj}
                      columnCustom={column.library.columnCustom}
                      libraryColumns={column.library.columns}
                      requires={main.groupAttributes.requires}
                      defaults={main.groupAttributes.defaults}
                      defaultSortColumns={MODULE_CONFIG.defaultSortColumns}
                      columns={column.columnObj.columns.columnsAlias}
                      groups={main.groupAttributes.groups}
                      isLoading={column.isLoading}
                    />
                    {/* {activeTab === 'owner' &&
                      (isEditByUser || isEditEverything) && (
                        <UploadV2 itemTypeId={MODULE_CONFIG.objectId} />
                      )} */}
                    {/* <IconButton
                      iconName="file_download"
                      size="24px"
                      onClick={
                        table.paging.totalRecord === 0
                          ? () => {}
                          : toggleModalDownload
                      }
                      isVertical
                      disabled={table.paging.totalRecord === 0}
                    >
                      {MAP_TITLE.actDownload.toUpperCase()}
                    </IconButton> */}
                  </div>
                  {/* <div className="downBlock">
                          <IconButton
                            iconName={showChart ? 'arrow-up' : 'arrow-down'}
                            size="24px"
                            onClick={() => setShowChart(prev => !prev)}
                            isVertical
                            disabled
                          />
                        </div> */}
                </WrapActionTable>
              </>
            </TableContainer>
          </TableWrapper>
        </TableRelative>
        {/* </LayoutContentLoading> */}
      </LayoutContent>
      <ModalDowload
        isOpen={isOpenModalDownload}
        toggle={toggleModalDownload}
        paging={table.paging}
        objectName="Allobjects"
        object_type={MODULE_CONFIG.objectType}
        ObjectServicesFn={BusinessObjectServices.businessObject.dowloadBO}
        // decryptFields={getItemAttributeDecryptFields(itemTypeId)}
        sort={table.sort}
        sortDefault="created_date"
        filters={filter}
        itemTypeId={MODULE_CONFIG.objectId}
        itemTypeName="user"
        columns={column.columnObj.columns.columnsAlias}
        getListType={activeTab === 'owner' ? 1 : 2}
      />
      <ModalArchive
        code="business_object"
        activeRows={table.selectedRows}
        isSelectedAll={table.isSelectedAll}
        totalRecord={table.paging.totalRecord}
        rules={filter.rules}
        label={getTranslateMessage(TRANSLATE_KEY._, 'business object')}
        placeHolderName=""
        callback={callback}
        // moduleName={MAP_TITLE.itemNameSegment}
        ObjectServicesFn={BusinessObjectServices.businessObject.archiveAllBO}
        isOpen={isOpenModalArchive}
        toggle={toggleModalArchive}
      />
      <ModalRecover
        activeRows={
          table.selectedRows && table.selectedRows.size === 0
            ? selectedRowCellStatus
            : table.selectedRows
        }
        isSelectedAll={table.isSelectedAll}
        totalRecord={table.paging.totalRecord}
        rules={filter.rules}
        label={getTranslateMessage(TRANSLATE_KEY._, 'Business Object')}
        placeHolderName=""
        callback={callback}
        // moduleName={MAP_TITLE.itemNameSegment}
        isOpen={isOpenModalRecover}
        toggle={toggleModalRecover}
      />
      {isOpenModalDelete && (
        <ModalDeleteMulti
          isUseAPICheck={false}
          // extendParams={{ insightPropertyId }}
          // isUseAPICheck
          activeRows={table.selectedRows}
          isSelectedAll={table.isSelectedAll}
          totalRecord={table.paging.totalRecord}
          rules={filter.rules}
          label={getTranslateMessage(TRANSLATE_KEY._, 'Remove Business Object')}
          placeHolderName=""
          moduleName={MAP_TITLE.title}
          ObjectServicesFn={BusinessObjectServices.businessObject.changeStatus}
          listTitleAcceptedFn={listTitleAcceptedFn}
          isOpenModal={isOpenModalDelete}
          setOpenModal={setIsOpenModalDelete}
          fetchData={props.fetchData}
          mapParamsFn={mapParamsDeleteFn}
          getTitleWarning={getTitleDeleteWarning}
        />
      )}

      {/* <ModalConfirm
        title={MAP_TITLE.titleWarningLimitation}
        isBorderBottom
        dividers={false}
        isFooterV2={true}
        isOpen={isOpenModalLimit}
        toggle={toggleModalLimit}
      >
        {MAP_TITLE.contentLimitation}
      </ModalConfirm> */}

      <DrawerCreateDataView
        isOpen={isOpenModalBO}
        callback={callback}
        toggleOpenDrawer={toggleModalBO}
        accountOwner={accountSelect}
      />
      {itemTypeId && (
        <DrawerDetailDataView
          itemTypeIdParams={itemTypeId}
          tab={tab}
          type={type}
          activeTab={activeTab}
          searchParams={searchParams}
          // isOpen={isOpenModalBO}
          // callback={callback}
          // toggleOpenDrawer={toggleModalBO}
          // accountOwner={accountSelect}
        />
      )}
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  main: makeSelectListObjectMain(),
  table: makeSelectListObjectTable(),
  filter: makeSelectListObjectFilter(),
  column: makeSelectListObjectColumn(),
  data: makeSelectListObjectData(),
  mapDataFooter: makeSelectListObjectMapDataFooter(),
  dateRange: makeSelectListObjectDateRange(),
  isLimitationPortal: makeSelectIsLimitationPortal(),
});

function mapDispatchToProps(dispatch) {
  return {
    init: params => {
      dispatch(init(PREFIX, params));
    },
    reset: params => {
      dispatch(reset(PREFIX, params));
    },
    fetchData: params => {
      dispatch(getList(PREFIX, params));
    },
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
    onChangeStatus: params => {
      dispatch(update(`${PREFIX}@@CELL_STATUS`, params));
    },
    onChangeDateRange: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@DATE_RANGE`, params));
    },
    toggleModalBusinessObject: () => {
      dispatch(
        updateValue(`${MODULE_CONFIG_DETAIL.key}@@MODAL_BUSINESS_OBJECT@@`),
      );
    },
    onChangeActionTable: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@ACTION_STATUS`, params));
    },
    onUpdateIsLimitation: params => {
      dispatch(updateValue(`${PREFIX}@@LIMITATION_PORTAL`, params));
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

const withReducer = injectReducer({ key: PREFIX, reducer });
const withSaga = injectSaga({ key: PREFIX, saga });

export default compose(
  withRouter,
  withReducer,
  withSaga,
  withConnect,
)(GroupsListting);
