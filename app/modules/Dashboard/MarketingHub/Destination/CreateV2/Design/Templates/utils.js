/* eslint-disable indent */
/* eslint-disable react/react-in-jsx-scope */
// Libraries
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';

// Utils
import { getDefaultVal } from 'components/common/InputLanguage/utils';
import { MAP_COMPONENT_ALIAS } from './utils.map';
import { MAP_INPUT_TYPE, MAP_VALIDATE } from '../utils.map';
import { initInputElement } from '../utils';
import { getValueDefault } from '../../../utils';
import { getObjectPropSafely } from '../../../../../../../utils/common';
import { addMessageToQueue } from '../../../../../../../utils/web/queue';

// Translations
import { getTranslateMessage } from '../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../messages/constant';

// Constants
import { CATALOG_CODES } from './constants';

const MAP_TITLE = {
  titlDestName: getTranslateMessage(
    TRANSLATE_KEY._TITL_DESTINATION_NAME,
    'Destination Name',
  ),
  titlDescription: getTranslateMessage(
    TRANSLATE_KEY._TITL_DESCRIPTION,
    'Description',
  ),
  titlDestCatalog: getTranslateMessage(
    TRANSLATE_KEY._TITL_DESTINATION_CATALOG,
    'Destination Catalog',
  ),
  titlMethod: getTranslateMessage(TRANSLATE_KEY._TITL_METHOD, 'Method'),
};

export const initDefaultDataConfig = () => ({
  isValidate: false,
  isExistedDestination: false,
  configFields: [],
  // generalFields: [],
  infoFields: ['destinationName', 'description', 'destinationCatalog'],
  extraInfoFields: [],
  destinationName: {
    ...initInputElement(
      'destinationName',
      {},
      255,
      true,
      MAP_TITLE.titlDestName,
      null,
      MAP_VALIDATE.multiLangInput,
    ),
    componentEl: MAP_INPUT_TYPE.multiLangInput,
    isTitleAlignLeft: true,
  },
  description: {
    ...initInputElement(
      'description',
      {},
      255,
      false,
      MAP_TITLE.titlDescription,
      null,
      MAP_VALIDATE.multiLangInput,
    ),
    componentEl: MAP_INPUT_TYPE.multiLangInput,
    isTitleAlignLeft: true,
  },
  destinationCatalog: {
    ...initInputElement(
      'destinationCatalog',
      null,
      null,
      true,
      MAP_TITLE.titlDestCatalog,
      null,
      MAP_VALIDATE.selectDropdown,
    ),
    componentEl: MAP_COMPONENT_ALIAS.destinationCatalog,
    options: [],
    mapOptions: {},
    placeHolder: '',
  },
  method: {
    ...initInputElement(
      'method',
      null,
      null,
      true,
      MAP_TITLE.titlMethod,
      null,
      MAP_VALIDATE.selectDropdown,
    ),
    componentEl: MAP_INPUT_TYPE.selectDropdown,
    options: [],
    mapOptions: {},
  },
  frequencyCapping: { value: {}, disabled: false },
  deliveryRate: { type: 'normal', disabled: false },
  templates: {
    selectedList: [],
    activeTemplate: '',
  },
});

export const toEntryAPI = dataIn => {
  // console.log('toEntryAPI', dataIn);
  const {
    destinationCatalog,
    description,
    destinationName,
    method,
    frequencyCapping,
    configFields,
    deliveryRate = {},
    templates = {},
  } = dataIn;
  const destinationSetting = {};
  const catalogCode = _get(destinationCatalog, 'value.catalogCode', '');

  configFields.forEach(each => {
    if (dataIn[each].inputType === 'select') {
      const dataSafe = getObjectPropSafely(() => dataIn[each].value.value);

      destinationSetting[each] = dataSafe;
    } else {
      destinationSetting[each] = dataIn[each].value;
    }
  });
  destinationSetting.deliveryRate =
    deliveryRate.type === 'normal'
      ? {
          type: deliveryRate.type,
        }
      : {
          type: deliveryRate.type,
          limit: deliveryRate.limit,
        };
  destinationSetting.method = method.value.value;

  if (catalogCode !== CATALOG_CODES.LINE_RICH_MENU) {
    destinationSetting.templates = templates;
  }
  // destinationSetting.frequencyCapping =
  //   toAPIFrequencyCapping(frequencyCapping.value) || {};
  destinationSetting.frequencyCapping = frequencyCapping.value;

  const data = {
    catalogId: destinationCatalog.value.value,
    destinationName: getDefaultVal(destinationName.value),
    destinationNameMultilang: destinationName.value || {},
    destinationSetting,
    description: getDefaultVal(description.value),
    descriptionMultilang: description.value || {},
  };

  // console.log('toEntryAPI out', data);
  return data;
};

export const mapValueToFE = ({ activeRow, dataConfig }) => {
  // console.log('mapValueToFE', activeRow, dataConfig);
  const {
    destinationNameMultilang,
    descriptionMultilang,
    catalogId,
    destinationSetting,
  } = activeRow;
  const {
    frequencyCapping,
    method,
    templates = {},
    deliveryRate = {},
  } = destinationSetting;

  const { configFields } = dataConfig;
  const data = {
    destinationName: destinationNameMultilang,
    description: descriptionMultilang,
    // frequencyCapping: toUIFrequencyCapping(frequencyCapping),
    templates,
    frequencyCapping,
    deliveryRate: { ...deliveryRate, disabled: false },
    destinationCatalog: dataConfig.destinationCatalog.mapOptions[catalogId],
    method: dataConfig.method.mapOptions[method],
  };
  configFields.forEach(each => {
    data[each] = destinationSetting[each] || '';

    if (!data[each]) {
      data[each] = getValueDefault(each, dataConfig);
    }
  });

  // console.log('toEntryAPI out', data);
  return data;
};

export const validateGeneralSetting = (dataConfig = {}) => {
  try {
    if (_isEmpty(dataConfig) || typeof dataConfig !== 'object') return false;

    const {
      infoFields = [],
      configFields = [],
      extraInfoFields = [],
    } = dataConfig;

    return [...infoFields, ...configFields, ...extraInfoFields].every(each =>
      getObjectPropSafely(() => dataConfig[each].isValidate, false),
    );
  } catch (error) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Destination/CreateV2/Design/Templates/utils.js',
      func: 'validateGeneralSetting',
      data: error.stack,
    });
    // eslint-disable-next-line no-console
    console.log(error);
  }
};
