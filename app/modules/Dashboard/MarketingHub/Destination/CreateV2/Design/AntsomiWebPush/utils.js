/* eslint-disable arrow-body-style */
/* eslint-disable indent */
/* eslint-disable react/react-in-jsx-scope */
import { getDefaultVal } from 'components/common/InputLanguage/utils';
import _ from 'lodash';
import { v4 as uuidV4 } from 'uuid';
import { MAP_COMPONENT_ALIAS } from './utils.map';
import { MAP_INPUT_TYPE, MAP_VALIDATE } from '../utils.map';
import { getTranslateMessage } from '../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../messages/constant';
import {
  CellActionMore,
  CellPromptName,
  CellText,
} from '../../../../../../../containers/Table/Cell';
import { initInputElement } from '../utils';
import {
  DIALOG_TYPES,
  initStatePushSlideSettings,
  LOCATION_OPTIONS,
  MESSAGE_TYPES,
  PREVIEW_STATE_LIST,
  PROMPT_TYPES,
  PROMPT_TYPES_OPTIONS,
  SIZE_OPTIONS,
  TOOLTIPS_OPTIONS,
} from './Content/_UI/Permission/utils';
import { random } from '../../../../../../../components/common/UIEditorPersonalization/utils.3rd';
import { COLUMNS_WIDTH } from '../../../../../../../containers/Table/constants';
import { getValueDefault } from '../../../utils';

const INIT_ID = random(6);

export const PROMPT_OPTIONS = [
  {
    label: getTranslateMessage(TRANSLATE_KEY._, 'Push Prompt'),
    value: 'PUSH',
    iconSub: 'settings',
    disabled: false,
  },
  {
    label: getTranslateMessage(TRANSLATE_KEY._, 'Email/Phone Prompt'),
    value: 'EMAIL/PHONE',
    iconSub: 'settings',
    disabled: true,
  },
];

const BEHAVIOR_OPTIONS = {
  matching: [
    {
      label: getTranslateMessage(
        TRANSLATE_KEY._,
        'Exact: (default) Opens a new window.',
      ),
      value: 'exact',
    },
    {
      label: getTranslateMessage(
        TRANSLATE_KEY._,
        'Origin: Take actions on a previous tab open to the same domain',
      ),
      value: 'origin',
    },
  ],
  action: [
    {
      label: getTranslateMessage(
        TRANSLATE_KEY._,
        'Navigate: (default) Navigate to new tab.',
      ),
      value: 'navigate',
    },
    {
      label: getTranslateMessage(
        TRANSLATE_KEY._,
        'Focus: Focus on existing tab.',
      ),
      value: 'focus',
    },
  ],
};

const initPropsToggleBtn = {
  name: '',
  value: false,
  label: '',
  isHiddenLabel: false,
  disabled: false,
  handleClick: () => {},
};

const MAP_TITLE = {
  titlDestName: getTranslateMessage(
    TRANSLATE_KEY._TITL_DESTINATION_NAME,
    'Destination Name',
  ),
  labelSiteName: getTranslateMessage(TRANSLATE_KEY._DES_SITE_NAME, 'Site Name'),
  labelSiteURL: getTranslateMessage(TRANSLATE_KEY._DES_SITE_URL, 'Site URL'),
  labelAutoReSub: getTranslateMessage(TRANSLATE_KEY._, 'Auto Re-subscribe'),
  labelAutoSub: getTranslateMessage(TRANSLATE_KEY._, '(HTTPS Only)'),
  labelIconURL: getTranslateMessage(
    TRANSLATE_KEY._DES_DEFAULT_ICON_URL,
    'Default Icon URL',
  ),
  labelWebhooks: getTranslateMessage(TRANSLATE_KEY._, 'Webhooks'),
  labelLink: getTranslateMessage(TRANSLATE_KEY._TITL_LINK, 'Link'),
  labelMessage: getTranslateMessage(TRANSLATE_KEY._DES_MESSAGE, 'Message'),
  labelServiceWorkers: getTranslateMessage(TRANSLATE_KEY._, 'Service workers'),
  labelEnableWebhooks: getTranslateMessage(TRANSLATE_KEY._, 'Enable webhooks'),
  labelPersistence: getTranslateMessage(TRANSLATE_KEY._, 'Persistence'),
  behavior: getTranslateMessage(TRANSLATE_KEY._, 'Click behavior'),
  swPath: getTranslateMessage(
    TRANSLATE_KEY._TITLE_SERVICE_WORKER,
    'Service worker',
  ),
  pathToSwFile: getTranslateMessage(
    TRANSLATE_KEY._TITLE_PATH_TO_SERVICE_WORKER_FILES,
    'Path to service workers files',
  ),
  labelCertificate: getTranslateMessage(TRANSLATE_KEY._, 'Safari Certificate'),
  titlDescription: getTranslateMessage(
    TRANSLATE_KEY._TITL_DESCRIPTION,
    'Description',
  ),
  titlDestCatalog: getTranslateMessage(
    TRANSLATE_KEY._TITL_DESTINATION_CATALOG,
    'Destination Catalog',
  ),
  titlMethod: getTranslateMessage(TRANSLATE_KEY._TITL_METHOD, 'Method'),
};
export const initDefaultDataConfig = () => ({
  isValidate: false,
  isExistedDestination: false,
  configFields: [],
  // generalFields: [],
  infoFields: ['destinationName', 'description', 'destinationCatalog'],
  infoSiteSetupFields: [
    'siteName',
    'siteURL',
    'autoReSub',
    'iconURL',
    'notHTTPS',
    'proxyOrigin',
  ],
  infoAdvancedSettingFields: [
    'swPath',
    'clickBehavior',
    // 'webhooks', // These options not use right now.
    // 'serviceWorkers',
    // 'persistence',
    // 'safariCertificate',
  ],
  infoNotificationFields: ['title', 'message', 'link', 'url'],
  extraInfoFields: [],
  siteName: {
    ...initInputElement(
      'siteName',
      '',
      null,
      true,
      MAP_TITLE.labelSiteName,
      null,
      MAP_VALIDATE.singleLineText,
    ),
    componentEl: MAP_INPUT_TYPE.singleLineText,
  },
  proxyOrigin: {
    ...initInputElement(
      'proxyOrigin',
      '',
      null,
      false,
      getTranslateMessage(TRANSLATE_KEY._, 'Choose a label'),
      null,
      MAP_VALIDATE.singleLineText,
    ),
    isHidden: true,
    componentEl: MAP_INPUT_TYPE.singleLineText,
  },
  title: {
    ...initInputElement(
      'title',
      '',
      null,
      false,
      getTranslateMessage(TRANSLATE_KEY._, 'Title'),
      null,
      MAP_VALIDATE.singleLineText,
    ),
    componentEl: MAP_INPUT_TYPE.singleLineText,
    isGridV2: true,
  },
  message: {
    ...initInputElement(
      'message',
      '',
      459,
      false,
      MAP_TITLE.labelMessage,
      null,
      MAP_VALIDATE.multiLineText,
      '',
    ),
    autoComplete: 1,
    canAddPersonalization: true,
    canImportFromMediaLibrary: false,
    dataType: 'string',
    default: '',
    inputFormat: 'string',
    inputType: 'text',
    componentEl: MAP_INPUT_TYPE.multiLineText,
    isGridV2: true,
  },
  siteURL: {
    ...initInputElement(
      'siteURL',
      '',
      null,
      true,
      MAP_TITLE.labelSiteURL,
      null,
      MAP_VALIDATE.singleLineText,
    ),
    componentEl: MAP_INPUT_TYPE.singleLineText,
  },
  autoReSub: {
    ...initInputElement(
      'autoReSub',
      false,
      null,
      false,
      MAP_TITLE.labelAutoReSub,
      null,
      MAP_VALIDATE.preventValidate,
    ),
    componentEl: MAP_COMPONENT_ALIAS.actionToggle,
    toggleProps: {
      ...initPropsToggleBtn,
      label: '',
      name: 'autoReSub',
      isHiddenLabel: true,
    },
    subLabel: MAP_TITLE.labelAutoSub,
  },
  iconURL: {
    ...initInputElement(
      'iconURL',
      '',
      null,
      false,
      MAP_TITLE.labelIconURL,
      null,
      MAP_VALIDATE.imageUrl,
    ),
    componentEl: MAP_INPUT_TYPE.imageUrl,
  },
  notHTTPS: {
    ...initInputElement(
      'notHTTPS',
      null,
      null,
      false,
      null,
      null,
      MAP_VALIDATE.preventValidate,
    ),
    componentEl: MAP_COMPONENT_ALIAS.actionToggle,
    toggleProps: {
      ...initPropsToggleBtn,
      name: 'notHTTPS',
      label: getTranslateMessage(TRANSLATE_KEY._, 'My site is not fully HTTPS'),
    },
    isHiddenTitle: true,
  },
  link: {
    ...initInputElement(
      'link',
      null,
      null,
      false,
      MAP_TITLE.labelLink,
      null,
      MAP_VALIDATE.preventValidate,
    ),
    componentEl: MAP_COMPONENT_ALIAS.actionToggle,
    toggleProps: {
      ...initPropsToggleBtn,
      label: getTranslateMessage(
        TRANSLATE_KEY._,
        'Open link when clicking welcome notification',
      ),
      name: 'link',
    },
    isGridV2: true,
  },
  url: {
    ...initInputElement(
      'url',
      '',
      null,
      false,
      '',
      null,
      MAP_VALIDATE.singleLineText,
    ),
    isGridV2: true,
    isHidden: true,
    placeholder: 'https://test-url.com',
    isHiddenLabel: true,
    componentEl: MAP_INPUT_TYPE.singleLineText,
  },
  swPath: {
    ...initInputElement(
      'swPath',
      '/sw.js',
      null,
      false,
      MAP_TITLE.swPath,
      null,
      MAP_VALIDATE.preventValidate,
    ),
    subLabel: MAP_TITLE.pathToSwFile,
    componentEl: MAP_INPUT_TYPE.singleLineText,
  },
  clickBehavior: {
    ...initInputElement(
      'clickBehavior',
      {},
      null,
      false,
      MAP_TITLE.behavior,
      null,
      MAP_VALIDATE.preventValidate,
    ),
    componentEl: MAP_COMPONENT_ALIAS.behavior,
    settings: {
      matchingStrategy: {
        options: BEHAVIOR_OPTIONS.matching,
        value: {
          label: getTranslateMessage(
            TRANSLATE_KEY._,
            'Exact: (default) Opens a new window',
          ),
          value: 'exact',
        },
      },
      actionStrategy: {
        options: BEHAVIOR_OPTIONS.action,
        value: {
          label: getTranslateMessage(
            TRANSLATE_KEY._,
            'Navigate: (default) Navigate to new tab.',
          ),
          value: 'navigate',
        },
      },
    },
  },
  // THESE OPTIONS NOT USE RIGHT NOW!!!
  // webhooks: {
  //   ...initInputElement(
  //     'webhooks',
  //     null,
  //     null,
  //     false,
  //     MAP_TITLE.labelWebhooks,
  //     null,
  //     MAP_VALIDATE.preventValidate,
  //   ),
  //   componentEl: MAP_COMPONENT_ALIAS.actionToggle,
  //   toggleProps: {
  //     ...initPropsToggleBtn,
  //     label: MAP_TITLE.labelEnableWebhooks,
  //     name: 'webhooks',
  //   },
  // },
  // serviceWorkers: {
  //   ...initInputElement(
  //     'serviceWorkers',
  //     null,
  //     null,
  //     false,
  //     MAP_TITLE.labelServiceWorkers,
  //     null,
  //     MAP_VALIDATE.preventValidate,
  //   ),
  //   componentEl: MAP_COMPONENT_ALIAS.actionToggle,
  //   toggleProps: {
  //     ...initPropsToggleBtn,
  //     label: getTranslateMessage(
  //       TRANSLATE_KEY.SOMETHING,
  //       'Customize service worker paths and filenames',
  //     ),
  //     name: 'serviceWorkers',
  //   },
  //   color: '#666666',
  // },
  // persistence: {
  //   ...initInputElement(
  //     'persistence',
  //     null,
  //     null,
  //     false,
  //     MAP_TITLE.labelPersistence,
  //     null,
  //     MAP_VALIDATE.preventValidate,
  //   ),
  //   componentEl: MAP_COMPONENT_ALIAS.actionToggle,
  //   toggleProps: {
  //     ...initPropsToggleBtn,
  //     label: getTranslateMessage(
  //       TRANSLATE_KEY.SOMETHING,
  //       'Notifications remain on screen until clicked',
  //     ),
  //     name: 'persistence',
  //   },
  //   color: '#666666',
  // },
  // safariCertificate: {
  //   ...initInputElement(
  //     'safariCertificate',
  //     null,
  //     null,
  //     false,
  //     MAP_TITLE.labelCertificate,
  //     null,
  //     MAP_VALIDATE.preventValidate,
  //   ),
  //   componentEl: MAP_COMPONENT_ALIAS.actionToggle,
  //   toggleProps: {
  //     ...initPropsToggleBtn,
  //     label: getTranslateMessage(
  //       TRANSLATE_KEY.SOMETHING,
  //       'Upload your own .p12 certificate',
  //     ),
  //     name: 'safariCertificate',
  //   },
  //   color: '#666666',
  // },
  destinationName: {
    ...initInputElement(
      'destinationName',
      {},
      255,
      true,
      MAP_TITLE.titlDestName,
      null,
      MAP_VALIDATE.multiLangInput,
    ),
    componentEl: MAP_INPUT_TYPE.multiLangInput,
  },
  description: {
    ...initInputElement(
      'description',
      {},
      255,
      false,
      MAP_TITLE.titlDescription,
      null,
      MAP_VALIDATE.multiLangInput,
    ),
    componentEl: MAP_INPUT_TYPE.multiLangInput,
  },
  destinationCatalog: {
    ...initInputElement(
      'destinationCatalog',
      null,
      null,
      true,
      MAP_TITLE.titlDestCatalog,
      null,
      MAP_VALIDATE.selectDropdown,
    ),
    componentEl: MAP_COMPONENT_ALIAS.destinationCatalog,
    options: [],
    mapOptions: {},
    placeHolder: 'sdnnsjdfg',
  },
  method: {
    ...initInputElement(
      'method',
      null,
      null,
      true,
      MAP_TITLE.titlMethod,
      null,
      MAP_VALIDATE.selectDropdown,
    ),
    componentEl: MAP_INPUT_TYPE.selectDropdown,
    options: [],
    mapOptions: {},
  },
  permissionPrompt: {
    promptTypeOptions: PROMPT_OPTIONS,
    data: [
      {
        prompt_id: INIT_ID,
        prompt_name: 'Push Slide Prompt',
        appearance: 'All pages, 1 pageviews, 10 seconds',
        action: 'Opens push slide prompt',
        prompt_code: 'PUSH',
        prompt_type: 'pushSlide',
        _action: 'sent',
      },
    ],
    table: {
      paging: {
        limits: [
          {
            value: 25,
            label: 25,
          },
          {
            value: 50,
            label: 50,
          },
          {
            value: 100,
            label: 100,
          },
        ],
        page: 1,
        limit: 25,
        totalRecord: 412,
      },
      sort: {
        key: 'utime',
        by: 'desc',
      },
      selectedRows: new Map(),
      isSelectedAll: false,
      expanded: {},
      isColumnsLoaded: true,
    },
    mapPrompts: {
      types: PROMPT_TYPES_OPTIONS,
      currentType: PROMPT_TYPES.SUBSCRIPTION,
      pushSlide: {},
      [PROMPT_TYPES.SUBSCRIPTION]: {
        previewStates: PREVIEW_STATE_LIST[PROMPT_TYPES.SUBSCRIPTION],
        mapPreviewState: {
          label: 'Preview State: Never Subscribed',
          value: 'never_subscribed',
        },
        subInfo: {
          size: {
            label: getTranslateMessage(TRANSLATE_KEY.SOMETHING, 'Size'),
            options: SIZE_OPTIONS,
          },
          location: {
            label: getTranslateMessage(TRANSLATE_KEY.SOMETHING, 'Location'),
            options: LOCATION_OPTIONS,
          },
          offsets: ['Bottom', 'Left', 'Right'],
        },
        tooltips: TOOLTIPS_OPTIONS,
        messages: MESSAGE_TYPES,
        dialogs: DIALOG_TYPES,
      },
      [PROMPT_TYPES.PUSH_SLIDE]: {
        previewStates: PREVIEW_STATE_LIST[PROMPT_TYPES.PUSH_SLIDE],
        mapPreviewState: {
          label: 'Preview State: Desktop Update',
          value: 'desktop_update',
        },
      },
    },
    prompts: {
      slidedown: {
        enabled: true,
        prompts: [initStatePushSlideSettings(INIT_ID)],
      },
    },
  },
  appId: uuidV4(),
  frequencyCapping: { value: {}, disabled: false },
});

/**
 * Render a list of components using the provided rendering component and configuration options.
 *
 * @function
 * @param {Object} classes - The css classes to apply to the rendered components.
 * @param {Object} configurations - An object containing configuration options for rendered components.
 * @param {Object} ComponentRendering - The component to use rendering each item in the array.
 * @param {Object} configurations.dataConfig - An object containing data configuration options for the rendered components.
 * @param {Array} configurations.infoFields - An array of strings representing the data fields to be rendered.
 * @param {Array} configurations.nameToggleList - An array of strings representing the data fields that should be rendered as toggle Elements.
 * @param {Function} configurations.onChange - A callback function to be executed when the value of any non-toggle element changes.
 * @param {Function} configurations.onToggle - A callback function to executed when the value of any toggle elements changes.
 *
 * @returns {Array} An array of rendered components.
 */
export const renderBlock = (classes, configurations, ComponentRendering) => {
  const {
    dataConfig = {},
    infoFields = [],
    nameToggleList = [],
    onChange = () => {},
    onToggle = () => {},
  } = configurations;

  return infoFields.map(each => {
    if (
      each === 'method' &&
      _.get(dataConfig, 'method.options.length', 0) === 1
    )
      return null;

    if (each !== 'destinationCatalog') {
      const isToggleElType = nameToggleList.includes(each);

      let configs = {
        ...dataConfig[each],
      };

      if (isToggleElType) {
        configs = {
          ...configs,
          toggleProps: {
            ...(dataConfig[each].toggleProps || {}),
            handleClick: onToggle,
          },
        };
      }

      const handleChange = isToggleElType ? () => {} : onChange;

      return (
        // eslint-disable-next-line react/react-in-jsx-scope
        <ComponentRendering
          key={each}
          {...configs}
          onChange={handleChange}
          classes={classes}
        />
      );
    }

    return null;
  });
};

export const toEntryAPI = dataIn => {
  // console.log('toEntryAPI', dataIn);
  const {
    destinationCatalog,
    description,
    destinationName,
    method,
    frequencyCapping,
    configFields,
    appId,
  } = dataIn;
  const destinationSetting = {};

  configFields.forEach(each => {
    destinationSetting[each] = dataIn[each].value;
  });
  destinationSetting.method = method.value.value;
  // destinationSetting.frequencyCapping =
  //   toAPIFrequencyCapping(frequencyCapping.value) || {};
  destinationSetting.frequencyCapping = frequencyCapping.value;
  destinationSetting.webpushConfig = {
    appId,
    swPath: _.get(dataIn, 'swPath.value', ''),
    autoResubscribe: dataIn.autoReSub.toggleProps.value,
    siteInfo: {
      name: dataIn.siteName.value,
      origin: dataIn.siteURL.value,
      defaultIconUrl: dataIn.iconURL.value,
      proxyOriginEnabled: dataIn.notHTTPS.toggleProps.value,
      proxyOrigin: dataIn.proxyOrigin.value,
    },
    promptsTable: dataIn.permissionPrompt.data,
    prompts: dataIn.permissionPrompt.prompts,
    welcomeNotification: {
      enable: true,
      title: dataIn.title.value,
      message: dataIn.message.value,
      urlEnabled: dataIn.link.toggleProps.value,
      url: dataIn.url.value,
    },
    // THESE OPTIONS NOT USE RIGHT NOW!!
    // webhooks: {
    //   enable: dataIn.webhooks.toggleProps.value,
    // },
    // serviceWorker: {
    //   enabled: dataIn.serviceWorkers.toggleProps.value,
    // },
    // certificates: {
    //       enabled: dataIn.safariCertificate.toggleProps.value,
    //     },
    notificationBehavior: {
      click: {
        match: dataIn.clickBehavior.settings.matchingStrategy.value.value,
        action: dataIn.clickBehavior.settings.actionStrategy.value.value,
      },
      // THIS OPTION NOT USE RIGHT NOW!!!
      // display: {
      //   persist: dataIn.persistence.toggleProps.value,
      // },
    },
  };

  const data = {
    catalogId: destinationCatalog.value.value,
    destinationName: getDefaultVal(destinationName.value),
    destinationNameMultilang: destinationName.value || {},
    destinationSetting,
    description: getDefaultVal(description.value),
    descriptionMultilang: description.value || {},
  };

  // console.log('toEntryAPI out', data);
  return data;
};

const mapDataFieldsToFE = configs => ({
  clickBehavior: configs.notificationBehavior.click,
  title: configs.welcomeNotification.title,
  message: configs.welcomeNotification.message,
  link: configs.welcomeNotification.urlEnabled,
  url: configs.welcomeNotification.url,
  siteName: configs.siteInfo.name,
  siteURL: configs.siteInfo.origin,
  proxyOrigin: configs.siteInfo.proxyOrigin,
  autoReSub: configs.autoResubscribe,
  iconURL: configs.siteInfo.defaultIconUrl,
  notHTTPS: configs.siteInfo.proxyOriginEnabled,
  appId: configs?.appId,
  swPath: configs?.swPath || '',
  // THESE OPTIONS CURRENT USE RIGHT NOW!!!
  // persistence: configs.notificationBehavior.display.persist,
  // safariCertificate: configs.certificates.enabled,
  // webhooks: configs.webhooks.enable,
  // serviceWorkers: configs.serviceWorker.enabled,
});

export const mapBehavior = (key, value) => {
  return BEHAVIOR_OPTIONS[key].find(item => item.value === value);
};

export const mapValueToFE = ({ activeRow, dataConfig }) => {
  // console.log('mapValueToFE', activeRow, dataConfig);
  const {
    destinationNameMultilang,
    descriptionMultilang,
    catalogId,
    destinationSetting,
  } = activeRow;
  const { frequencyCapping, method, webpushConfig } = destinationSetting;

  const { configFields } = dataConfig;
  const data = {
    destinationName: destinationNameMultilang,
    description: descriptionMultilang,
    // frequencyCapping: toUIFrequencyCapping(frequencyCapping),
    frequencyCapping,
    destinationCatalog: dataConfig.destinationCatalog.mapOptions[catalogId],
    method: dataConfig.method.mapOptions[method],
    prompts: {
      promptsTable: webpushConfig.promptsTable,
      prompts: webpushConfig.prompts,
    },
    ...mapDataFieldsToFE(webpushConfig),
  };
  configFields.forEach(each => {
    data[each] = destinationSetting[each] || '';

    if (!data[each]) {
      data[each] = getValueDefault(each, dataConfig);
    }
  });

  // console.log('toEntryAPI out', data);
  return data;
};

export const tableColumns = [
  {
    Header: getTranslateMessage(TRANSLATE_KEY._, 'Prompt'),
    id: 'prompt_name',
    accessor: 'prompt_name',
    disableSortBy: true,
    placement: 'left',
    sticky: 'left',
    width: COLUMNS_WIDTH.MAIN,
    minWidth: COLUMNS_WIDTH.MAIN,
    disableResizing: true,
    Cell: CellPromptName,
  },
  {
    Header: 'Info Remaining',
    columns: [
      {
        Header: getTranslateMessage(TRANSLATE_KEY._, 'Appearance'),
        id: 'appearance',
        accessor: 'appearance',
        Cell: CellText,
        placement: 'left',
        disableSortBy: true,
        minWidth: COLUMNS_WIDTH.DEFAULT,
      },
      {
        Header: getTranslateMessage(TRANSLATE_KEY._, 'Action'),
        id: 'action',
        accessor: 'action',
        Cell: CellText,
        placement: 'left',
        disableSortBy: true,
        minWidth: COLUMNS_WIDTH.DEFAULT,
      },
      {
        Header: getTranslateMessage(TRANSLATE_KEY._, 'Action'),
        id: '_action',
        accessor: '_action',
        Cell: CellActionMore,
        placement: 'left',
        className: 'action-custom-size',
        disableSortBy: true,
        minWidth: COLUMNS_WIDTH.DEFAULT,
      },
    ],
  },
];
