/* eslint-disable indent */
/* eslint-disable react/react-in-jsx-scope */
// Libraries
import { v4 as uuidV4 } from 'uuid';
import _isEmpty from 'lodash/isEmpty';

// Utils
import { getDefaultVal } from 'components/common/InputLanguage/utils';
import { addMessageToQueue } from '../../../../../../../utils/web/queue';
import { MAP_COMPONENT_ALIAS } from './utils.map';
import { MAP_INPUT_TYPE, MAP_VALIDATE } from '../utils.map';
import { initInputElement } from '../utils';
import { getObjectPropSafely } from '../../../../../../../utils/common';
import { getValueDefault } from '../../../utils';

// Translations
import { getTranslateMessage } from '../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../messages/constant';

// Constants
import { AUTHENTICATION_OPTIONS, AUTH_TYPES } from './constants';
import { TEMPLATE_KEYS } from 'containers/UIPreview/AppPushPreview/AppPushTemplate/constants';

const MAP_TITLE = {
  titlDestName: getTranslateMessage(
    TRANSLATE_KEY._TITL_DESTINATION_NAME,
    'Destination Name',
  ),
  titlDescription: getTranslateMessage(
    TRANSLATE_KEY._TITL_DESCRIPTION,
    'Description',
  ),
  titlDestCatalog: getTranslateMessage(
    TRANSLATE_KEY._TITL_DESTINATION_CATALOG,
    'Destination Catalog',
  ),
  titlMethod: getTranslateMessage(TRANSLATE_KEY._TITL_METHOD, 'Method'),
  titleAuthenticationType: getTranslateMessage(
    TRANSLATE_KEY.SOMETHING,
    'APNs Authentication Type',
  ),
  titleKeyFile: getTranslateMessage(TRANSLATE_KEY.SOMETHING, 'Key .p8 file'),
  titleKeyId: getTranslateMessage(TRANSLATE_KEY.SOMETHING, 'Key ID'),
  titleTeamId: getTranslateMessage(TRANSLATE_KEY.SOMETHING, 'Team ID'),
  titleAppBundleId: getTranslateMessage(
    TRANSLATE_KEY.SOMETHING,
    'App Bundle ID',
  ),
};

export const getTypeField = type =>
  type === AUTH_TYPES.AUTH_KEY ? 'authFields' : 'certificateFields';

export const initDefaultDataConfig = () => ({
  isValidate: false,
  isExistedDestination: false,
  configFields: [],
  // generalFields: [],
  infoFields: ['destinationName', 'description', 'destinationCatalog'],
  extraInfoFields: [],
  APNFields: ['authenticationType'],
  authFields: ['keyFile', 'keyId', 'teamId', 'bundleId'],
  // certificateFields: ['certificateFile', 'certificatePassword'],
  FCMFields: ['serviceAccount', 'senderId'],
  templateSettingFields: ['templateSetting'],
  destinationName: {
    ...initInputElement(
      'destinationName',
      {},
      255,
      true,
      MAP_TITLE.titlDestName,
      null,
      MAP_VALIDATE.multiLangInput,
    ),
    componentEl: MAP_INPUT_TYPE.multiLangInput,
  },
  description: {
    ...initInputElement(
      'description',
      {},
      255,
      false,
      MAP_TITLE.titlDescription,
      null,
      MAP_VALIDATE.multiLangInput,
    ),
    componentEl: MAP_INPUT_TYPE.multiLangInput,
  },
  destinationCatalog: {
    ...initInputElement(
      'destinationCatalog',
      null,
      null,
      true,
      MAP_TITLE.titlDestCatalog,
      null,
      MAP_VALIDATE.selectDropdown,
    ),
    componentEl: MAP_COMPONENT_ALIAS.destinationCatalog,
    options: [],
    mapOptions: {},
    placeHolder: '',
  },
  method: {
    ...initInputElement(
      'method',
      null,
      null,
      true,
      MAP_TITLE.titlMethod,
      null,
      MAP_VALIDATE.selectDropdown,
    ),
    componentEl: MAP_INPUT_TYPE.selectDropdown,
    options: [],
    mapOptions: {},
  },
  authenticationType: {
    ...initInputElement(
      'authenticationType',
      AUTHENTICATION_OPTIONS[0],
      null,
      true,
      MAP_TITLE.titleAuthenticationType,
      true,
      MAP_VALIDATE.selectDropdown,
    ),
    isValidate: true,
    componentEl: MAP_INPUT_TYPE.selectDropdown,
    options: AUTHENTICATION_OPTIONS,
    mapOptions: {
      [AUTH_TYPES.AUTH_KEY]: AUTHENTICATION_OPTIONS[0],
    },
  },
  keyFile: {
    ...initInputElement(
      'keyFile',
      '',
      null,
      true,
      MAP_TITLE.titleKeyFile,
      null,
      MAP_VALIDATE.multiLineText,
    ),
    componentEl: MAP_COMPONENT_ALIAS.uploadFile,
    encrypt: true,
  },
  keyId: {
    ...initInputElement(
      'keyId',
      '',
      null,
      true,
      MAP_TITLE.titleKeyId,
      null,
      MAP_VALIDATE.singleLineText,
    ),
    componentEl: MAP_INPUT_TYPE.singleLineText,
  },
  teamId: {
    ...initInputElement(
      'teamId',
      '',
      null,
      true,
      MAP_TITLE.titleTeamId,
      null,
      MAP_VALIDATE.singleLineText,
    ),
    componentEl: MAP_INPUT_TYPE.singleLineText,
  },
  bundleId: {
    ...initInputElement(
      'bundleId',
      '',
      null,
      true,
      MAP_TITLE.titleAppBundleId,
      null,
      MAP_VALIDATE.singleLineText,
    ),
    componentEl: MAP_INPUT_TYPE.singleLineText,
  },
  // certificateFile: {
  //   ...initInputElement(
  //     'certificateFile',
  //     false,
  //     null,
  //     true,
  //     getTranslateMessage(TRANSLATE_KEY._, 'Certificate .p12 file'),
  //     null,
  //     MAP_VALIDATE.singleLineText,
  //   ),
  //   componentEl: MAP_INPUT_TYPE.singleLineText,
  // },
  // certificatePassword: {
  //   ...initInputElement(
  //     'certificatePassword',
  //     false,
  //     null,
  //     true,
  //     getTranslateMessage(TRANSLATE_KEY._, 'Certificate Password'),
  //     null,
  //     MAP_VALIDATE.singleLineText,
  //   ),
  //   componentEl: MAP_INPUT_TYPE.singleLineText,
  // },
  serviceAccount: {
    ...initInputElement(
      'serviceAccount',
      '',
      null,
      true,
      getTranslateMessage(TRANSLATE_KEY._, 'Service Account'),
      null,
      MAP_VALIDATE.multiLineText,
    ),
    componentEl: MAP_COMPONENT_ALIAS.uploadFile,
    encrypt: true,
  },
  senderId: {
    ...initInputElement(
      'senderId',
      '',
      null,
      true,
      getTranslateMessage(TRANSLATE_KEY._, 'Sender ID'),
      null,
      MAP_VALIDATE.singleLineText,
    ),
    componentEl: MAP_INPUT_TYPE.singleLineText,
  },
  templateSetting: {
    ...initInputElement(
      'templateSetting',
      {
        templates: Object.values(TEMPLATE_KEYS),
        selected: TEMPLATE_KEYS.BASIC_NOTIFICATION,
      },
      null,
      false,
      '',
      null,
      MAP_VALIDATE.templateSetting,
    ),
    componentEl: MAP_INPUT_TYPE.templateSetting,
  },
  appId: uuidV4(),
  frequencyCapping: { value: {}, disabled: false },
});

export const toEntryAPI = dataIn => {
  // console.log('toEntryAPI', dataIn);
  const {
    destinationCatalog,
    description,
    destinationName,
    method,
    frequencyCapping,
    configFields,
    authenticationType = {},
    authFields = [],
    FCMFields = [],
    // templateSettingFields = [],
    appId = '',
  } = dataIn;
  const destinationSetting = {};

  configFields.forEach(each => {
    destinationSetting[each] = dataIn[each].value;
  });
  destinationSetting.method = method.value.value;
  // destinationSetting.frequencyCapping =
  //   toAPIFrequencyCapping(frequencyCapping.value) || {};
  destinationSetting.frequencyCapping = frequencyCapping.value;

  const authSetting = {};
  const FCMSetting = {};
  authFields.forEach(item => {
    authSetting[item] = getObjectPropSafely(() => dataIn[item].value, '');
  });
  FCMFields.forEach(item => {
    FCMSetting[item] = getObjectPropSafely(() => dataIn[item].value, '');
  });

  destinationSetting.templates = {
    selectedList: getObjectPropSafely(
      () =>
        dataIn.templateSetting.value.templates.map(template => ({
          id: template,
        })),
      [],
    ),
  };

  destinationSetting.appPushConfig = {
    APNSetting: {
      authType: getObjectPropSafely(() => authenticationType.value.value, ''),
      ...authSetting,
    },
    FCMSetting,
    appId,
  };

  const data = {
    catalogId: destinationCatalog.value.value,
    destinationName: getDefaultVal(destinationName.value),
    destinationNameMultilang: destinationName.value || {},
    destinationSetting,
    description: getDefaultVal(description.value),
    descriptionMultilang: description.value || {},
  };

  // console.log('toEntryAPI out', data);
  return data;
};

const mapDataFieldsToFE = configs => {
  const { FCMSetting = {}, APNSetting = {}, ...rest } = configs;
  const result = { ...rest };

  const { authType = '', ...restSetting } = APNSetting;
  const authenticationType =
    AUTHENTICATION_OPTIONS.find(item => item.value === authType) ||
    AUTHENTICATION_OPTIONS[0];

  return {
    ...result,
    ...FCMSetting,
    ...restSetting,
    authenticationType,
    serviceAccount: FCMSetting.serviceAccount || '',
  };
};

const mapTemplateSettingToFE = destinationSetting => {
  return {
    templates: getObjectPropSafely(
      () => destinationSetting.templates.selectedList.map(({ id }) => id),
      [],
    ),
    selected: getObjectPropSafely(
      () => destinationSetting.templates.selectedList[0].id,
      null,
    ),
  };
};

export const mapValueToFE = ({ activeRow, dataConfig }) => {
  // console.log('mapValueToFE', activeRow, dataConfig);
  const {
    destinationNameMultilang,
    descriptionMultilang,
    catalogId,
    destinationSetting,
  } = activeRow;
  const { frequencyCapping, method, appPushConfig } = destinationSetting;

  const { configFields } = dataConfig;

  const data = {
    destinationName: destinationNameMultilang,
    description: descriptionMultilang,
    // frequencyCapping: toUIFrequencyCapping(frequencyCapping),
    frequencyCapping,
    destinationCatalog: dataConfig.destinationCatalog.mapOptions[catalogId],
    method: dataConfig.method.mapOptions[method],
    templateSetting: mapTemplateSettingToFE(destinationSetting),
    ...mapDataFieldsToFE(appPushConfig),
  };
  configFields.forEach(each => {
    data[each] = destinationSetting[each] || '';

    if (!data[each]) {
      data[each] = getValueDefault(each, dataConfig);
    }
  });

  // console.log('toEntryAPI out', data);
  return data;
};

export const validateGeneralSetting = (dataConfig = {}) => {
  try {
    if (_isEmpty(dataConfig) || typeof dataConfig !== 'object') return false;

    const {
      infoFields = [],
      configFields = [],
      extraInfoFields = [],
    } = dataConfig;

    return [...infoFields, ...configFields, ...extraInfoFields].every(each =>
      getObjectPropSafely(() => dataConfig[each].isValidate, false),
    );
  } catch (error) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Destination/CreateV2/Design/AntsomiAppPush/utils.js',
      func: 'validateGeneralSetting',
      data: error.stack,
    });
    // eslint-disable-next-line no-console
    console.log(error);
  }
};
