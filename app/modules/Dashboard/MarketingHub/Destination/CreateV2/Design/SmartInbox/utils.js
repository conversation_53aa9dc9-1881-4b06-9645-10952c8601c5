/* eslint-disable indent */
/* eslint-disable react/react-in-jsx-scope */
// Libraries
import { v4 as uuidV4 } from 'uuid';
import _isEmpty from 'lodash/isEmpty';
import _has from 'lodash/has';
import _set from 'lodash/set';
import { Map } from 'immutable';

// Utils
import { getDefaultVal } from 'components/common/InputLanguage/utils';
import { MAP_COMPONENT_ALIAS } from './utils.map';
import { MAP_INPUT_TYPE, MAP_VALIDATE } from '../utils.map';
import { initInputElement } from '../utils';
import { getObjectPropSafely } from '../../../../../../../utils/common';
import { addMessageToQueue } from '../../../../../../../utils/web/queue';
import { getValueDefault } from '../../../utils';

// Translations
import { getTranslateMessage } from '../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../messages/constant';

// Constants
import {
  BACKGROUNDS,
  BORDERS,
  CATEGORY_STYLE_TYPES,
  COLORS,
  DEFAULT_STYLE_TYPES,
  DIMENSIONS,
  DISPLAY_SETTING_TYPES,
  FONTS,
  NOTIFY_STATUS,
  STYLE_KEYS,
  SMART_INBOX_TYPE,
  TOGGLE_ICON_TYPES,
  TOGGLE_STYLE_TYPES,
  WEB_VIEW_SETTING_TABS,
  CUSTOM_STYLE_KEYS,
  WEB_VIEW_CHILD_TABS,
  TEMPLATES,
  CUSTOM_COLORS,
  HARD_MESSAGE_CONTENT,
  DEFAULT_SYSTEM_ICON,
  EXPIRED_MESSAGE,
  DEFAULT_EXPIRED_MESSAGE,
} from './constants';
import { isEmpty } from 'lodash';

const MAP_TITLE = {
  titlDestName: getTranslateMessage(
    TRANSLATE_KEY._TITL_DESTINATION_NAME,
    'Destination Name',
  ),
  titlDescription: getTranslateMessage(
    TRANSLATE_KEY._TITL_DESCRIPTION,
    'Description',
  ),
  titlDestCatalog: getTranslateMessage(
    TRANSLATE_KEY._TITL_DESTINATION_CATALOG,
    'Destination Catalog',
  ),
  titlMethod: getTranslateMessage(TRANSLATE_KEY._TITL_METHOD, 'Method'),
};

const initialStyleSetting = (attributeList = [], customAttribute = {}) => {
  const result = {};

  if (!_isEmpty(attributeList) && Array.isArray(attributeList)) {
    attributeList.forEach(each => {
      if (each === STYLE_KEYS.BG_COLOR) result[each] = '#ffffff';
      if (each === CUSTOM_STYLE_KEYS.BG_COLOR_ALPHA) result[each] = 100;
      if (each === STYLE_KEYS.COLOR) result[each] = '#005eb8';
      if (each === STYLE_KEYS.COLOR_ALPHA) result[each] = 100;
      if (each === STYLE_KEYS.FONT_SIZE) result[each] = 10;
      if (each === STYLE_KEYS.FONT_FAMILY) result[each] = 'Roboto';
      if (each === STYLE_KEYS.FONT_WEIGHT) result[each] = 700;
      if (each === STYLE_KEYS.BORDER_COLOR) result[each] = '#ffffff';
      if (each === CUSTOM_STYLE_KEYS.BORDER_COLOR_ALPHA) result[each] = 100;
      if (each === STYLE_KEYS.BORDER_RADIUS) result[each] = 20;
      if (each === STYLE_KEYS.WIDTH) result[each] = 260;
      if (each === STYLE_KEYS.HEIGHT) result[each] = 450;

      // Assign custom data for each key
      if (_has(customAttribute, each)) {
        result[each] = customAttribute[each];
      }
    });
  }

  return result;
};

export const initialWebViewSettingsState = () => ({
  [WEB_VIEW_CHILD_TABS.TOGGLE]: {
    type: TOGGLE_STYLE_TYPES.POPUP,
    iconType: TOGGLE_ICON_TYPES.SYSTEM,
    systemIcon: 'fas bell',
    systemIconSvg: DEFAULT_SYSTEM_ICON,
    uploadIcon: '',
    iconStyling: initialStyleSetting([STYLE_KEYS.COLOR, STYLE_KEYS.FONT_SIZE], {
      color: '#ffffff',
      fontSize: 20,
    }),
    badgeStyling: initialStyleSetting(
      [STYLE_KEYS.BG_COLOR, STYLE_KEYS.WIDTH].concat(FONTS, COLORS),
      {
        width: 20,
        fontSize: 12,
        color: '#ffffff',
        backgroundColor: '#ff0000',
      },
    ),
    contentAnimation: {
      animationType: 'none',
      animationDelay: 0,
      animationDuration: 1000,
      animationIterationStyle: 'infinite',
      animationIterationCount: 1,
    },
    position: 'right',
    notificationSpacing: {
      positionSuffix: 'px',
      linkedPositionInput: false,
      top: 'auto',
      right: 25,
      bottom: 10,
      left: 'auto',
    },
    slideDirection: 'slide-up',
  },
  [WEB_VIEW_CHILD_TABS.NOTI_BOX]: {
    container: initialStyleSetting(
      [STYLE_KEYS.BORDER_RADIUS].concat(DIMENSIONS, BACKGROUNDS),
      {
        borderRadius: 3,
      },
    ),
    header: {
      ...initialStyleSetting(FONTS.concat(COLORS, BACKGROUNDS), {
        fontSize: 16,
        color: '#000000',
        fontWeight: 400,
      }),
      content: 'Notification',
    },
    messageIndicator: {
      ...initialStyleSetting(DEFAULT_STYLE_TYPES, {
        fontSize: 12,
        color: '#ffffff',
        backgroundColor: '#005eb8',
      }),
      content: `${HARD_MESSAGE_CONTENT} new message`,
    },
    category: {
      type: CATEGORY_STYLE_TYPES.ROUND,
      isShowGeneralTab: true,
      content: 'All',
      normal: initialStyleSetting(DEFAULT_STYLE_TYPES, {
        borderColor: '#005eb8',
      }),
      clicked: initialStyleSetting(DEFAULT_STYLE_TYPES, {
        color: '#ffffff',
        backgroundColor: '#005eb8',
      }),
    },
    unreadIndicator: initialStyleSetting([...BORDERS, ...BACKGROUNDS], {
      backgroundColor: '#68e63e',
      borderColorAlpha: 0,
    }),
    bubble: {
      template: Object.values(TEMPLATES),
      selectedTemplate: 'announcements',
    },
    expiredMessage: {
      type: EXPIRED_MESSAGE.AUTO_HIDE.value,
      visualIndicator: DEFAULT_EXPIRED_MESSAGE,
    },
  },
});

export const initDefaultDataConfig = () => ({
  isValidate: false,
  isExistedDestination: false,
  configFields: [],
  // generalFields: [],
  infoFields: ['destinationName', 'description', 'destinationCatalog'],
  extraInfoFields: [],
  destinationName: {
    ...initInputElement(
      'destinationName',
      {},
      255,
      true,
      MAP_TITLE.titlDestName,
      null,
      MAP_VALIDATE.multiLangInput,
    ),
    componentEl: MAP_INPUT_TYPE.multiLangInput,
  },
  description: {
    ...initInputElement(
      'description',
      {},
      255,
      false,
      MAP_TITLE.titlDescription,
      null,
      MAP_VALIDATE.multiLangInput,
    ),
    componentEl: MAP_INPUT_TYPE.multiLangInput,
  },
  destinationCatalog: {
    ...initInputElement(
      'destinationCatalog',
      null,
      null,
      true,
      MAP_TITLE.titlDestCatalog,
      null,
      MAP_VALIDATE.selectDropdown,
    ),
    componentEl: MAP_COMPONENT_ALIAS.destinationCatalog,
    options: [],
    mapOptions: {},
    placeHolder: '',
  },
  method: {
    ...initInputElement(
      'method',
      null,
      null,
      true,
      MAP_TITLE.titlMethod,
      null,
      MAP_VALIDATE.selectDropdown,
    ),
    componentEl: MAP_INPUT_TYPE.selectDropdown,
    options: [],
    mapOptions: {},
  },
  APIToken: uuidV4(),
  webviewSettings: Map(initialWebViewSettingsState()),
  customColors: CUSTOM_COLORS,
  frequencyCapping: { value: {}, disabled: false },
  destinationAntsomi: {
    appPush: null,
    webPush: null,
  },
});

export const toEntryAPI = dataIn => {
  // console.log('toEntryAPI', dataIn);
  const {
    destinationCatalog,
    description,
    destinationName,
    method,
    frequencyCapping,
    configFields,
    APIToken = '',
    customColors = [],
    webviewSettings = Map({}),
    destinationAntsomi,
    activeTabDisplay,
    SDK,
  } = dataIn;
  const destinationSetting = {};

  configFields.forEach(each => {
    if (dataIn[each].inputType === 'select') {
      const dataSafe = getObjectPropSafely(() => dataIn[each].value.value);

      destinationSetting[each] = dataSafe;
    } else {
      destinationSetting[each] = dataIn[each].value;
    }
  });
  destinationSetting.method = method.value.value;
  // destinationSetting.frequencyCapping =
  //   toAPIFrequencyCapping(frequencyCapping.value) || {};
  destinationSetting.frequencyCapping = frequencyCapping.value;
  destinationSetting.smartInboxConfigs = {
    APIToken,
    customColors,
    // webviewSettings: webviewSettings.toObject(),
    destinationAntsomi: { ...destinationAntsomi },
  };
  // if (activeTabDisplay === 'website_view') {
  //   destinationSetting.smartInboxConfigs.webviewSettings = webviewSettings.toObject();
  // } else if (activeTabDisplay === 'android_view') {
  //   destinationSetting.smartInboxConfigs.androidSettings = SDK.value;
  // } else if (activeTabDisplay === 'ios_view') {
  //   destinationSetting.smartInboxConfigs.iosSettings = SDK.value;
  // }

  destinationSetting.smartInboxConfigs.webviewSettings = webviewSettings.toObject();
  destinationSetting.smartInboxConfigs.androidSettings = SDK.value;
  destinationSetting.smartInboxConfigs.iosSettings = SDK.value;

  const data = {
    catalogId: destinationCatalog.value.value,
    destinationName: getDefaultVal(destinationName.value),
    destinationNameMultilang: destinationName.value || {},
    destinationSetting,
    description: getDefaultVal(description.value),
    descriptionMultilang: description.value || {},
  };

  // console.log('toEntryAPI out', data);
  return data;
};

const mapDataFieldsToFE = configs => {
  const {
    APIToken = '',
    customColors = [],
    webviewSettings = {},
    iosSettings,
    androidSettings,
    destinationAntsomi,
  } = configs;

  if (!isEmpty(webviewSettings)) {
    const expiredMessage = getObjectPropSafely(
      () => webviewSettings.notificationBox.expiredMessage,
      null,
    );

    if (!expiredMessage) {
      const defaultExpiredMessage = initialWebViewSettingsState()
        .notificationBox.expiredMessage;

      _set(
        webviewSettings,
        'notificationBox.expiredMessage',
        defaultExpiredMessage,
      );
    }
  }

  return {
    APIToken,
    customColors,
    webviewSettings: Map(webviewSettings),
    iosSettings,
    androidSettings,
    destinationAntsomi,
  };
};

export const mapValueToFE = ({ activeRow, dataConfig }) => {
  // console.log('mapValueToFE', activeRow, dataConfig);
  const {
    destinationNameMultilang,
    descriptionMultilang,
    catalogId,
    destinationSetting,
  } = activeRow;
  const { frequencyCapping, method, smartInboxConfigs } = destinationSetting;

  const { configFields } = dataConfig;
  const data = {
    destinationName: destinationNameMultilang,
    description: descriptionMultilang,
    // frequencyCapping: toUIFrequencyCapping(frequencyCapping),
    frequencyCapping,
    destinationCatalog: dataConfig.destinationCatalog.mapOptions[catalogId],
    method: dataConfig.method.mapOptions[method],
    ...mapDataFieldsToFE(smartInboxConfigs),
  };
  configFields.forEach(each => {
    data[each] = destinationSetting[each] || '';

    if (!data[each]) {
      data[each] = getValueDefault(each, dataConfig);
    }
  });

  // console.log('toEntryAPI out', data);
  return data;
};

export const initialState = () => ({
  isInitConfigureFielded: false,
  activeTabDisplay: DISPLAY_SETTING_TYPES.WEB_VIEW.key,
  activeWebViewTab: WEB_VIEW_SETTING_TABS.TOGGLE.key,
  verifyNotification: {
    isLoading: false,
    isFirstTime: false,
    appPush: {
      status: NOTIFY_STATUS.FAIL,
      id: null,
    },
    webPush: {
      status: NOTIFY_STATUS.FAIL,
      id: null,
    },
  },
});

export const mapCodeType = catalogCode => {
  try {
    switch (catalogCode) {
      case 'antsomi_web_push': {
        return SMART_INBOX_TYPE.WEB_PUSH;
      }
      case 'antsomi_app_push': {
        return SMART_INBOX_TYPE.APP_PUSH;
      }
      default: {
        return '';
      }
    }
  } catch (error) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Destination/CreateV2/Design/SmartInbox/utils.js',
      func: 'mapCodeType',
      data: error.stack,
    });
    // eslint-disable-next-line no-console
    console.log(error);
  }
};

export const validateGeneralSetting = (dataConfig = {}) => {
  try {
    if (_isEmpty(dataConfig) || typeof dataConfig !== 'object') return false;

    const {
      infoFields = [],
      configFields = [],
      extraInfoFields = [],
    } = dataConfig;

    return [...infoFields, ...configFields, ...extraInfoFields].every(each =>
      getObjectPropSafely(() => dataConfig[each].isValidate, false),
    );
  } catch (error) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Destination/CreateV2/Design/SmartInbox/utils.js',
      func: 'validateGeneralSetting',
      data: error.stack,
    });
    // eslint-disable-next-line no-console
    console.log(error);
  }
};
export const getParamListingDestination = channelId => ({
  data: {
    page: 1,
    limit: 1000,
    search: '',
    sort: 'ctime',
    sd: 'desc',
    columns: [
      'status',
      'destination_name',
      'catalog_id',
      'utime',
      'ctime',
      'c_user_id',
      'channel_id',
      'destination_id',
      'u_user_id',
      'catalog_status',
      'destination_status',
      'is_broadcast',
      'logo_url',
      'owner_id',
    ],
    perf_columns: [],
    filters: {
      OR: [
        {
          AND: [
            {
              type: 1,
              column: 'channel_code_id',
              data_type: 'number',
              operator: 'matches',
              value: [channelId],
            },
            {
              type: 1,
              column: 'status',
              data_type: 'number',
              operator: 'matches',
              value: [1],
            },
          ],
        },
      ],
    },
    getListType: 1,
  },
});
export const mapDataFn = item => ({
  ...item,
  id: item.destination_id,
  value: item.destination_id,
  label: item.destination_name,
});
export const mapBluePrint = (dataIn, bluePrintFn) => {
  const data = {
    list: [],
    map: {},
  };
  dataIn.forEach(item => {
    if (
      item.catalog_id === 'Antsomi Web Push' ||
      item.catalog_id === 'Antsomi App Push'
    ) {
      const objItem = {
        ...item,
        ...bluePrintFn(item),
      };
      data.list.push(objItem);
      data.map[objItem.id] = objItem;
    }
  });
  return data;
};
export const getActiveType = dataIn => {
  if (dataIn.iosSettings) {
    return 'ios_view';
  }
  if (dataIn.androidSettings) {
    return 'android_view';
  }
  if (dataIn.webviewSettings) {
    return 'website_view';
  }
};
