/* eslint-disable indent */
import { isProduction, safeParse } from '../../../../utils/common';
import { isLocalhost } from '../../../../utils/web/utils';
import AMServices from 'services/AnalyticsModel';

export const configToAPISuggestion = ({
  objectType,
  limit = 100,
  page = 1,
  sort = 'asc',
  search,
  filtersBody = {},
  isGetOnlySuggestParams = false,
  propertyCode,
  feServices = 'suggestionMultilang',
  feKey = '',
  isFilters = false,
}) => {
  const dataOut = {
    objectType,
    limit,
    page,
    sort,
    search,
    filters: isFilters && {
      OR: [
        {
          AND: [],
        },
      ],
    },
    isGetOnlySuggestParams: isGetOnlySuggestParams && 0,
    propertyCode,
    feServices,
    feKey,
  };
  // console.log('dataOut', dataOut);
  return dataOut;
};
export const JOURNEY_CHART_METRIC = 'story-list-default-metrics';
export function getColumnsExport(column, main, type) {
  const columns = [];
  column.columnObj.columns.columnsAlias.forEach(each => {
    const { map } = main.groupAttributes;
    if (map[each] && map[each].type !== 2 && map[each].type !== 3) {
      columns.push(each);
    }
  });
  if (!columns.includes(type)) {
    columns.push(type);
  }
  // console.log(columns);
  return columns;
}

export const getViewType = key =>
  safeParse(JSON.parse(localStorage.getItem(`view-type-${key}`)), 'table');

export const setViewType = (key, value) =>
  localStorage.setItem(`view-type-${key}`, JSON.stringify(value));

export const getDomainInsight = () => {
  const envProduction = isProduction();
  const domain = envProduction
    ? 'https://insights.antsomi.com'
    : 'https://sandbox-antalyser.antsomi.com';
  return domain;
};

export const getBuildTimeWithModelId = async modelId => {
  const data = {
    scope: 'analytic-model',
    filters: {
      OR: [
        {
          AND: [],
        },
      ],
    },
    columns: [
      'compute_id',
      'trigger_type',
      'compute_status',
      'compute_start_time',
      'compute_end_time',
      'compute_durations',
      'response_code',
    ],
    model_id: [modelId],
    limit: 1,
    page: 1,
    sort: 'utime',
    sd: 'desc',
  };
  let result = '';
  const res = await AMServices.computation.getList({ data });
  if (res.code === 200 && res.data.length) {
    result = res.data[0]['compute_end_time'];
  }
  return result;
};
export const MAP_LABEL_CHANNEL = {
  1: 'Email',
  2: 'Web Personalization',
  3: 'Web Notification',
  4: 'App Notification',
  5: 'FB Messenger',
  6: 'Webhook',
  7: 'SMS',
  9: 'Viber',
  10: 'Zalo',
  11: 'File Transfer',
  12: 'Smart Inbox',
  13: 'WhatsApp',
  14: 'Telegram',
  15: 'Line',
};

export const getModuleConfigByChannelId = (channelId, isNewUI) => {
  const CONFIG_KEY = {
    CREATE: 'destination-create',
    ANTSOMI_APP_PUSH: 'destination-create-v2-app-push',
    ANTSOMI_EMAIL: 'destination-create-v2-email',
    ANTSOMI_SMART_INBOX: 'destination-create-v2-smart-inbox',
    ANTSOMI_WEB_PUSH: 'destination-create-v2-web-push',
    ANTSOMI_TEMPLATE: 'destination-create-v2-templates',
  };
  if (isNewUI) {
    switch (+channelId) {
      case 1: {
        return CONFIG_KEY.ANTSOMI_EMAIL;
      }
      case 3: {
        return CONFIG_KEY.ANTSOMI_WEB_PUSH;
      }
      case 4: {
        return CONFIG_KEY.ANTSOMI_APP_PUSH;
      }
      case 12: {
        return CONFIG_KEY.ANTSOMI_SMART_INBOX;
      }
      case 10:
      case 15: {
        return CONFIG_KEY.ANTSOMI_TEMPLATE;
      }
      default:
        return CONFIG_KEY.CREATE;
    }
  }
  return CONFIG_KEY.CREATE;
};

export const getValueDefault = (name, dataConfig) => {
  if (!dataConfig?.[name] || dataConfig[name]?.inputType !== 'radio') return '';
  return dataConfig[name]?.default || '';
};
