/* eslint-disable indent */
/* eslint-disable react/prop-types */
import React from 'react';
import _isEmpty from 'lodash/isEmpty';
import _ from 'lodash';
import {
  UITextField,
  UITippy,
  UIWrapperDisable as WrapperDisable,
  UICheckbox,
  UINumber,
} from '@xlab-team/ui-components';
import TextareaAutosize from '@material-ui/core/TextareaAutosize';
// import UISelect from 'components/form/UISelectCondition';
import UISelect from 'components/form/UISelectCondition';
import PairKeyValue from 'components/common/UIPairKeyValue';
import InputLanguage from 'components/common/InputLanguage';
import { getDefaultVal } from 'components/common/InputLanguage/utils';
// import UIModalInsert from 'components/common/UIInsertMedia';
import FormHelperText from '@material-ui/core/FormHelperText';
import ErrorOutlineIcon from '@material-ui/icons/ErrorOutline';
import TinymceEditor from 'components/common/UIEditorPersonalization/WrapperPersonalization';
import IconXlab from 'components/common/UIIconXlab';
import { UploadImage, Input, Icon, RadioGroup } from '@antscorp/antsomi-ui';
import {
  Title,
  WrapperCenterFlexStart,
  WrapperCenterFlexEnd,
  Wrapper,
  WrapperIcon,
  ButtonPre,
  WrapperExtraBtn,
} from './styles';
import {
  getObjectPropSafely,
  isProduction,
  safeParseInt,
  validateEmail,
} from '../../../../../utils/web/utils';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
// eslint-disable-next-line import/no-cycle
import {
  CHANNEL_FILE_TRANSFER,
  EXTRA_EMAIL_FIELDS,
  mapLabelExtraEmail,
} from './utils';
// import InputMacro from '../../../../../components/Atoms/InputWithMacro';
// import { EDITOR_SETTINGS_DEFAULTS } from '../../../../../components/Atoms/InputWithMacro/settings';
import { getCurrentUserId, getToken } from '../../../../../utils/web/cookie';
import InputPreview from '../../../../../components/Atoms/InputPreview';
import {
  BlockLeft,
  BlockRight,
} from 'containers/Drawer/DrawerIntegration/components/Content/styled';
import TextareaProtected from '../../../../../components/Molecules/TextareaProtected';

const MAP_TRANSLATE = {
  nameEmpty: getTranslateMessage(
    TRANSLATE_KEY._NOTI_EMPTY_NAME,
    `Name can't be empty`,
  ),
  inputEmpty: getTranslateMessage(
    TRANSLATE_KEY._NOTI_REQUIRED_INPUT,
    'Input can’t be empty',
  ),
  requiredField: getTranslateMessage(
    TRANSLATE_KEY._NOTI_EMPTY_NAME,
    `This field can't be empty`,
  ),
};

const { Password } = Input;

const DOMAIN_MEDIA_SANDBOX = 'https://sandbox-media-template.antsomi.com/cdp';
const DOMAIN_MEDIA_PROD = 'https://media-template.antsomi.com';

export const MAP_VALIDATE = {
  singleLineText: ({ value, isRequired, maxLength }) => {
    let [isValidate, errors] = [true, []];
    if (value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else if (value.trim().length === 0 && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.spaceError];
    } else if (maxLength !== null && value.length > maxLength) {
      isValidate = false;
      errors = [`Invalid: maximum ${maxLength} characters`];
    }
    return { errors, isValidate };
  },
  imageUrl: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  editor: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (typeof value === 'string' && value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else if (
      typeof value === 'object' &&
      !Object.prototype.hasOwnProperty.call(value, 'html') &&
      isRequired
    ) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else if (
      typeof value === 'object' &&
      Object.prototype.hasOwnProperty.call(value, 'html') &&
      value.html.trim() === '' &&
      isRequired
    ) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  htmlEditor: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (typeof value === 'string' && value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else if (
      typeof value === 'object' &&
      !Object.prototype.hasOwnProperty.call(value, 'html') &&
      isRequired
    ) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else if (
      typeof value === 'object' &&
      Object.prototype.hasOwnProperty.call(value, 'html') &&
      value.html.trim() === '' &&
      isRequired
    ) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  multiLineText: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  selectDropdown: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (_isEmpty(value) && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  email: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (value.trim() === '' && isRequired) {
      isValidate = false;
    } else {
      isValidate = validateEmail(value);
      errors = isValidate ? [] : ['Invalid email'];
    }
    return { errors, isValidate };
  },
  password: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.requiredField];
    }
    return { errors, isValidate };
  },
  keyvalue: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (_isEmpty(value) && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else {
      isValidate = Object.keys(value).every(
        each => each.trim() !== '' && value[each].trim() !== '',
      );
      errors = isValidate ? [] : [MAP_TRANSLATE.inputEmpty];
    }

    return { errors, isValidate };
  },
  checkbox: () => ({ errors: [], isValidate: true }),
  radioGroup: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];

    if (!value && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.requiredField];
    }
    return { errors, isValidate };
  },
  default: () => ({ errors: [], isValidate: true }),
  number: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (Number.isNaN(value) && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  multiLangInput: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (_isEmpty(getDefaultVal(value).trim()) && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  singleLineAddPersonalize: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
};

export const MAP_INPUT_TYPE = {
  singleLineText: props => {
    const { extraEmailBtn = [] } = props;
    const labelCode = getObjectPropSafely(() => props.labelCode, '');
    const isChannelEmail = safeParseInt(props.channelId) === 1;
    const isExtraEmailField =
      isChannelEmail && Object.values(EXTRA_EMAIL_FIELDS).includes(props.name);

    // Force hide some field of channel email
    if (
      isExtraEmailField &&
      Array.isArray(extraEmailBtn) &&
      extraEmailBtn.includes(props.name)
    )
      return null;

    return (
      <>
        <BlockLeft
          item
          sm={3}
          className={props.classes.title}
          style={{ ...(props.styleLeftContent || {}) }}
        >
          <WrapperCenterFlexEnd>
            <Title>
              {props.label}
              <span style={{ color: '#ff0000' }}>
                {props.isRequired && ` *`}
              </span>
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.5rem', minWidth: '1.75rem' }} />
            )}
          </WrapperCenterFlexEnd>
        </BlockLeft>
        <BlockRight
          item
          sm={9}
          style={{
            ...(isExtraEmailField ? { position: 'relative' } : {}),
            ...(props.styleRightContent || {}),
          }}
        >
          <InputPreview
            type="input"
            value={props.value}
            isViewMode={props.isJourneyTemplateMode}
          >
            {/* {labelCode === '_CATALOG_DAC_FILE_NAME' ? ( */}
            {/*   <> */}
            {/*     <InputMacro */}
            {/*       disabled={props.disabled} */}
            {/*       value={props.value} */}
            {/*       isError={!!props.errors[0]} */}
            {/*       onChange={props.onChange(props.name)} */}
            {/*       debounce={400} */}
            {/*       settings={{ */}
            {/*         ...EDITOR_SETTINGS_DEFAULTS, */}
            {/*         objectName: props.name, */}
            {/*         enableLinter: false, */}
            {/*       }} */}
            {/*     /> */}
            {/*     <FormHelperText */}
            {/*       id="component-helper-text" */}
            {/*       error={!!props.errors[0]} */}
            {/*       style={{ fontSize: '0.688rem', marginTop: 4 }} */}
            {/*     > */}
            {/*       {props.errors} */}
            {/*     </FormHelperText> */}
            {/*   </> */}
            {/* ) : ( */}
            {props.encrypt ? (
              <>
                <Password
                  id={props.name}
                  value={props.value}
                  disabled={props.disabled || props.isViewMode}
                  placeholder={props.placeholder}
                  status={props.errors[0] ? 'error' : undefined}
                  onChange={e => {
                    if (e?.target) {
                      const valueOut = e.target?.value;
                      props.onChange(props.name)(valueOut);
                    }
                  }}
                  visibilityToggle={props.isOwner}
                  onFocus={e => {
                    if (
                      !props.isOwner &&
                      props.isHasPermissionEdit &&
                      e.target.type === 'password'
                    ) {
                      e.target.oldValue = e.target.value;
                      props.onChange(props.name)('');
                      e.target.type = 'text';
                    }
                  }}
                  onBlur={e => {
                    if (!props.isOwner && props.isHasPermissionEdit) {
                      if (!e.target.value) {
                        props.onChange(props.name)(e.target.oldValue);
                        e.target.type = 'password';
                      }
                    }
                  }}
                />
                <FormHelperText
                  id="component-helper-text"
                  error={!!props.errors[0]}
                  style={{ fontSize: '11px', marginTop: 4 }}
                >
                  {props.errors}
                </FormHelperText>
              </>
            ) : (
              <UITextField
                id={props.name}
                value={props.value}
                onChange={props.onChange(props.name)}
                firstText={props.errors[0]}
                placeholder={props.placeholder}
                textFieldProps={{
                  disabled: props.disabled,
                  size: 'small',
                  multiline: false,
                  rowsMax: 1,
                  className: 'width-100',
                  // id: 'standard-basic',
                  error: !!props.errors[0],
                }}
              />
            )}
            {/* )} */}
            {isChannelEmail && isExtraEmailField && (
              <WrapperDisable disabled={props.disabled}>
                <WrapperIcon
                  style={{ bottom: '0px', right: '-35px' }}
                  onClick={() => {
                    if (typeof props.onChangeExtraEmailBtn === 'function') {
                      props.onChangeExtraEmailBtn({
                        type: 'REMOVE',
                        btnName: props.name,
                      });
                    }
                  }}
                >
                  <IconXlab name="close" color="#005eb8" fontSize="18px" />
                </WrapperIcon>
              </WrapperDisable>
            )}
          </InputPreview>
        </BlockRight>
      </>
    );
  },
  password: props => {
    return (
      <>
        <BlockLeft
          item
          sm={3}
          className={props.classes.title}
          style={{ ...(props.styleLeftContent || {}) }}
        >
          <WrapperCenterFlexEnd>
            <Title>
              {props.label}
              <span style={{ color: '#ff0000' }}>
                {props.isRequired && ` *`}
              </span>{' '}
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.5rem', minWidth: '1.75rem' }} />
            )}
          </WrapperCenterFlexEnd>
        </BlockLeft>
        <BlockRight
          item
          sm={9}
          style={{
            ...(props.styleRightContent || {}),
          }}
        >
          <InputPreview
            type="password"
            isViewMode={props.isViewMode}
            value={props.value}
          >
            <Password
              disabled={props.disabled || props.isViewMode}
              placeholder={props.placeholder}
              id={props.name}
              value={props.value}
              status={props.errors[0] ? 'error' : undefined}
              onChange={e => {
                if (e?.target) {
                  const valueOut = e.target?.value;
                  props.onChange(props.name)(valueOut);
                }
              }}
              visibilityToggle={props.encrypt ? props.isOwner : true}
              onFocus={e => {
                if (
                  !props.isOwner &&
                  props.isHasPermissionEdit &&
                  e.target.type === 'password'
                ) {
                  e.target.oldValue = e.target.value;
                  props.onChange(props.name)('');
                  e.target.type = 'text';
                }
              }}
              onBlur={e => {
                if (!props.isOwner && props.isHasPermissionEdit) {
                  if (!e.target.value) {
                    props.onChange(props.name)(e.target.oldValue);
                    e.target.type = 'password';
                  }
                }
              }}
            />
            <FormHelperText
              id="component-helper-text"
              error={!!props.errors[0]}
              style={{ fontSize: '11px', marginTop: 4 }}
            >
              {props.errors}
            </FormHelperText>
          </InputPreview>
        </BlockRight>
      </>
    );
  },
  imageUrl: props => {
    // console.log('imageUrl', props);

    // Constants
    const userId = getCurrentUserId();
    const token = getToken();

    return (
      <>
        <BlockLeft item sm={3}>
          <WrapperCenterFlexEnd>
            <Title className={props.classes.title}>
              {props.label}
              <span style={{ color: '#ff0000' }}>
                {props.isRequired && ` *`}
              </span>{' '}
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.5rem', minWidth: '1.75rem' }} />
            )}
          </WrapperCenterFlexEnd>
        </BlockLeft>
        <BlockRight item sm={9}>
          <UploadImage
            isInputMode
            domainMedia={
              isProduction() ? DOMAIN_MEDIA_PROD : DOMAIN_MEDIA_SANDBOX
            }
            slug="api/v1"
            paramConfigs={{
              token,
              userId,
              accountId: userId,
            }}
            width="100%"
            selectedImage={{
              url: props.value,
            }}
            onChangeImage={image => {
              props.onChange(props.name)((image && image.url) || '');
            }}
            onRemoveImage={() => {
              props.onChange(props.name)('');
            }}
          />
          {/* <UIModalInsert
            value={props.value}
            initData={props.initValue}
            onChange={props.onChange(props.name)}
            // onChange={() => {}}
          /> */}
        </BlockRight>
      </>
    );
  },
  selectDropdown: props => {
    const { value = {}, options = [] } = props;
    let valueParsed = value;

    // Vì hiện tại có một số case đặc biệt value === string nên phải map lại data từ options
    if (typeof valueParsed === 'string' && Array.isArray(options)) {
      valueParsed = options.find(option => option.value === value);
    }

    const previewInput = _.get(props, ['value', 'label'], '');

    return (
      <>
        <BlockLeft item sm={3} style={{ ...(props.styleLeftContent || {}) }}>
          <WrapperCenterFlexEnd>
            <Title className={props.classes.title}>
              {props.label}
              <span style={{ color: '#ff0000' }}>
                {props.isRequired && ` *`}
              </span>{' '}
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.75rem', width: '100%' }} />
            )}
          </WrapperCenterFlexEnd>
        </BlockLeft>
        <BlockRight item sm={9} style={{ ...(props.styleRightContent || {}) }}>
          <InputPreview
            isViewMode={props.isViewMode}
            type="input"
            value={previewInput}
          >
            <UISelect
              onlyParent
              use="tree"
              isSearchable
              options={props.options}
              value={valueParsed || value}
              onChange={props.onChange(props.name)}
              placeholder={getTranslateMessage(
                TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                'Select an item',
              )}
              fullWidthPopover
              disabled={props.disabled}
            />
            <FormHelperText
              id="component-helper-text"
              style={{ fontSize: '11px', marginTop: 4 }}
              error={!!props.errors[0]}
            >
              {props.errors}
            </FormHelperText>
          </InputPreview>
        </BlockRight>
      </>
    );
  },
  multiLineText: props => {
    const { allowUpload = false, uploadConfig = {} } = props;
    const accept = _.get(
      uploadConfig,
      ['accept'],
      'text/*,application/json,.py',
    );
    const channelId = getObjectPropSafely(() => props.channelId, '');
    const handleChangeInputFile = event => {
      const file = event.target.files[0];
      const reader = new FileReader();
      reader.onload = e => {
        const fileContent = e.target.result;
        // const jsonData = JSON.parse(fileContent);
        if (
          fileContent &&
          typeof props.onChange === 'function' &&
          typeof props.onChange(props.name) === 'function'
        ) {
          props.onChange(props.name)(fileContent);
        }
      };
      reader.readAsText(file);
    };

    const handleChange = value => {
      props.onChange(props.name)(value);
    };

    return (
      <>
        <BlockLeft item sm={3} style={{ ...(props.styleLeftContent || {}) }}>
          <WrapperCenterFlexStart>
            <Title className={props.classes.title}>
              {props.label}
              <span style={{ color: '#ff0000' }}>
                {props.isRequired && ` *`}
              </span>{' '}
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.75rem', width: '100%' }} />
            )}
          </WrapperCenterFlexStart>
        </BlockLeft>
        <BlockRight item sm={9}>
          <WrapperDisable disabled={props.disabled}>
            <TextareaProtected
              {...props}
              allowUpload={allowUpload}
              accept={accept}
              channelId={channelId}
              onChangeInputFile={handleChangeInputFile}
              onChange={handleChange}
            />
            {/* <Wrapper>
              <TextareaAutosize
                aria-label="minimum height"
                rowsMin={6}
                rowsMax={12}
                placeholder=""
                value={props.value}
                onChange={evt => props.onChange(props.name)(evt.target.value)}
                style={{
                  width: '100%',
                  minHeight: 150,
                  maxHeight: 200,
                  padding: 4,
                  border: 'none',
                }}
              />
              {(+channelId === CHANNEL_FILE_TRANSFER || allowUpload) && (
                <WrapperIcon htmlFor="file-transfer">
                  <Icon
                    type="icon-ants-file-upload"
                    size={24}
                    color="#005eb8"
                  />
                  <input
                    type="file"
                    id="file-transfer"
                    name="file-transfer"
                    accept={accept}
                    style={{ width: 0, height: 0 }}
                    onChange={handleChangeInputFile}
                  />
                </WrapperIcon>
              )}
            </Wrapper> */}
          </WrapperDisable>
          <FormHelperText
            style={{ fontSize: '11px', marginTop: 4 }}
            id="component-helper-text"
            error={!!props.errors[0]}
          >
            {props.errors}
          </FormHelperText>
        </BlockRight>
      </>
    );
  },
  editor: props => {
    return (
      <>
        <BlockLeft item sm={3} style={{ ...(props.styleLeftContent || {}) }}>
          <WrapperCenterFlexStart>
            <Title>
              {props.label}
              <span style={{ color: '#ff0000' }}>
                {props.isRequired && ` *`}
              </span>{' '}
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.75rem', width: '100%' }} />
            )}
          </WrapperCenterFlexStart>
        </BlockLeft>
        <BlockRight item sm={9}>
          <WrapperDisable disabled={props.disabled}>
            <TinymceEditor
              {...props}
              initData={props.initValue}
              onChange={props.onChange(props.name)}
              typeComponent="editor"
              // showPopupEditorHTML={false}
              showPopupEditorHTML
              showPersonalization
              showObjectWidget
            />
          </WrapperDisable>
          <FormHelperText
            style={{ fontSize: '11px', marginTop: 4 }}
            id="component-helper-text"
            error={!!props.errors[0]}
          >
            {props.errors}
          </FormHelperText>
        </BlockRight>
      </>
    );
  },
  keyvalue: props => {
    return (
      <>
        <BlockLeft item sm={3} style={{ ...(props.styleLeftContent || {}) }}>
          <WrapperCenterFlexStart>
            <Title>
              {props.label}
              <span style={{ color: '#ff0000' }}>
                {props.isRequired && ` *`}
              </span>{' '}
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.75rem', width: '100%' }} />
            )}
          </WrapperCenterFlexStart>
        </BlockLeft>
        <BlockRight item sm={9}>
          <WrapperDisable disabled={props.disabled}>
            <PairKeyValue
              {...props}
              global={{ ...props }}
              errors={props.errors}
              isRequired={props.isRequired}
              initData={props.initValue}
              onChange={value => {
                const onChange = props.onChange(props.name);

                if (onChange) {
                  onChange(value);
                }
              }}
            />
          </WrapperDisable>
        </BlockRight>
      </>
    );
  },
  checkbox: props => {
    return (
      <>
        <BlockLeft item sm={3} />
        <BlockRight item sm={9}>
          <WrapperDisable disabled={props.disabled}>
            <UICheckbox
              name={props.name}
              checked={!!props.value}
              onChange={value => props.onChange(props.name)(value)}
            >
              <>{props.label}</>
            </UICheckbox>
          </WrapperDisable>
        </BlockRight>
      </>
    );
  },
  htmlEditor: props => {
    return (
      <>
        <BlockLeft item sm={3} style={{ ...(props.styleLeftContent || {}) }}>
          <WrapperCenterFlexStart>
            <Title>
              {props.label}
              <span style={{ color: '#ff0000' }}>
                {props.isRequired && ` *`}
              </span>{' '}
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.75rem', width: '100%' }} />
            )}
          </WrapperCenterFlexStart>
        </BlockLeft>
        <BlockRight item sm={9}>
          <WrapperDisable disabled={props.disabled}>
            <TinymceEditor
              {...props}
              initData={props.initValue}
              onChange={props.onChange(props.name)}
              typeComponent="htmlEditor"
              showPersonalization
              showPopupEditorHTML
              showObjectWidget
            />
          </WrapperDisable>
          <FormHelperText
            style={{ fontSize: '11px', marginTop: 4 }}
            id="component-helper-text"
            error={!!props.errors[0]}
          >
            {props.errors}
          </FormHelperText>
        </BlockRight>
      </>
    );
  },
  multiLangInput: props => {
    return (
      <>
        <BlockLeft
          item
          sm={3}
          className={props.classes.title}
          style={{ ...(props.styleLeftContent || {}) }}
        >
          <WrapperCenterFlexEnd>
            <Title>
              {props.label}
              <span style={{ color: '#ff0000' }}>
                {props.isRequired && ` *`}
              </span>{' '}
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.5rem', minWidth: '1.75rem' }} />
            )}
          </WrapperCenterFlexEnd>
        </BlockLeft>
        <BlockRight item sm={9} style={{ ...(props.styleRightContent || {}) }}>
          <InputLanguage
            disabled={props.disabled}
            initData={props.initValue}
            onChange={props.onChange(props.name)}
            errors={props.errors}
            isViewMode={props.isViewMode}
            isJourneyTemplateMode={props.isJourneyTemplateMode}
          />
        </BlockRight>
      </>
    );
  },
  radioGroup: props => {
    const isError = !!props?.errors[0];
    const defaultValue = _.get(props, 'options[0].value', props.default);

    return (
      <>
        <BlockLeft item sm={3}>
          <WrapperCenterFlexStart>
            <Title>
              {props.label}
              <span style={{ color: '#ff0000' }}>
                {props.isRequired && ` *`}
              </span>
            </Title>
            {isError && <div style={{ height: '1.75rem', width: '100%' }} />}
          </WrapperCenterFlexStart>
        </BlockLeft>
        <BlockRight item sm={9}>
          <RadioGroup
            style={
              props.style ?? {
                display: 'flex',
                flexDirection: 'column',
                gap: 8,
              }
            }
            options={props.options}
            value={props.value}
            defaultValue={defaultValue}
            disabled={props.disabled}
            onChange={event => {
              if (event?.target) {
                const newValue = event.target.value;
                props.onChange(props.name)(newValue);
              }
            }}
          />
          {isError && (
            <FormHelperText
              style={{ fontSize: '11px', marginTop: 4 }}
              id="component-helper-text"
              error={!!props.errors[0]}
            >
              {props.errors}
            </FormHelperText>
          )}
        </BlockRight>
      </>
    );
  },
  number: props => {
    return (
      <>
        <BlockLeft item sm={3}>
          <WrapperCenterFlexStart>
            <Title>
              {props.label}
              <span style={{ color: '#ff0000' }}>
                {props.isRequired && ` *`}
              </span>{' '}
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.75rem', width: '100%' }} />
            )}
          </WrapperCenterFlexStart>
        </BlockLeft>
        <BlockRight item sm={9}>
          <WrapperDisable disabled={props.disabled}>
            <UINumber
              type="number"
              onChange={props.onChange(props.name)}
              value={props.value}
              min={props.minLength}
              max={props.maxLength}
              width="4rem"
            />
          </WrapperDisable>
          <FormHelperText
            style={{ fontSize: '11px', marginTop: 4 }}
            id="component-helper-text"
            error={!!props.errors[0]}
          >
            {props.errors}
          </FormHelperText>
        </BlockRight>
      </>
    );
  },
  singleLineAddPersonalize: props => {
    const { extraEmailBtn = [] } = props;
    const isChannelEmail = safeParseInt(props.channelId) === 1;

    return (
      <>
        <BlockLeft
          item
          sm={3}
          className={props.classes.title}
          style={{ ...(props.styleLeftContent || {}) }}
        >
          <WrapperCenterFlexEnd>
            <Title>
              {props.label}
              <span style={{ color: '#ff0000' }}>
                {props.isRequired && ` *`}
              </span>{' '}
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.5rem', minWidth: '1.75rem' }} />
            )}
          </WrapperCenterFlexEnd>
        </BlockLeft>
        <BlockRight item sm={9} style={{ ...(props.styleRightContent || {}) }}>
          <WrapperDisable disabled={props.disabled}>
            <TinymceEditor
              {...props}
              typeComponent="input"
              onChange={props.onChange(props.name)}
              initData={props.initValue}
              isDestination
            />
            {isChannelEmail &&
              props.name === 'email' &&
              Array.isArray(extraEmailBtn) && (
                <WrapperExtraBtn>
                  {extraEmailBtn.map(each => (
                    <ButtonPre
                      key={each}
                      iconName="add"
                      iconSize="20px"
                      reverse
                      theme="outline"
                      isNoBackround
                      style={{ border: 'none' }}
                      onClick={() => {
                        if (typeof props.onChangeExtraEmailBtn === 'function') {
                          props.onChangeExtraEmailBtn({
                            type: 'ADD',
                            btnName: each,
                          });
                        }
                      }}
                    >
                      {mapLabelExtraEmail(each)}
                    </ButtonPre>
                  ))}
                </WrapperExtraBtn>
              )}
          </WrapperDisable>
          <FormHelperText
            id="component-helper-text"
            error={!!props.errors[0]}
            style={{ fontSize: '11px', marginTop: 4 }}
          >
            {props.errors}
          </FormHelperText>
        </BlockRight>
      </>
    );
  },
};

export const getTypeRender = ({
  inputType,
  inputFormat,
  canImportFromMediaLibrary,
  canAddPersonalization,
}) => {
  // console.log(
  //   'typeRender: ',
  //   inputType,
  //   inputFormat,
  //   canImportFromMediaLibrary,
  //   canAddPersonalization,
  // );
  let typeRender = 'singleLineText';
  if (inputType === 'multitext') {
    typeRender = 'multiLineText';
  } else if (inputType === 'keyvalue') {
    typeRender = 'keyvalue';
  } else if (inputType === 'select') {
    typeRender = 'selectDropdown';
  } else if (inputType === 'radio') {
    typeRender = 'radioGroup';
  } else if (inputType === 'editor') {
    typeRender = 'editor';
  } else if (inputType === 'text') {
    if (inputFormat === 'password') {
      typeRender = 'password';
    } else if (canAddPersonalization) {
      typeRender = 'singleLineAddPersonalize';
    } else if (canImportFromMediaLibrary) {
      typeRender = 'imageUrl';
    }
  }

  // console.log('typeRender: ', typeRender);
  return typeRender;
};

export const MAP_COMPONENT_ALIAS = {
  destinationCatalog: props => {
    return (
      <>
        <BlockLeft item sm={3}>
          <WrapperCenterFlexEnd>
            <Title className={props.classes.title}>
              {props.label}
              <span style={{ color: '#ff0000' }}>
                {props.isRequired && ` *`}
              </span>{' '}
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.75rem', width: '100%' }} />
            )}
          </WrapperCenterFlexEnd>
        </BlockLeft>
        <BlockRight item sm={9}>
          <div style={{ position: 'relative' }}>
            <UISelect
              // display="label"
              onlyParent
              use="tree"
              isSearchable
              options={props.options}
              value={props.value}
              onChange={props.onChange(props.name)}
              placeholder={getTranslateMessage(
                TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                'Select an item',
              )}
              fullWidthPopover
              disabled={props.disabled}
            />
            <div style={{ position: 'absolute', right: '-2rem', top: '0rem' }}>
              <UITippy content={props.tooltip || ''} arrow distance={10}>
                <ErrorOutlineIcon />
              </UITippy>
            </div>
          </div>
          <FormHelperText
            style={{ fontSize: '11px', marginTop: 4 }}
            id="component-helper-text"
            error={!!props.errors[0]}
          >
            {props.errors}
          </FormHelperText>
        </BlockRight>
        {/* <Grid item sm={1}> */}

        {/* </Grid> */}
      </>
    );
  },
};

export const createTooltip = ({
  basicSettings,
  settings,
  basicInputs,
  inputs,
}) => (
  <>
    <p>
      <b>Config Field: </b>
      {basicSettings.map(each => settings[each].label).join(', ')}
    </p>
    <p>
      <b>Content Field: </b>
      {basicInputs.map(each => inputs[each].label).join(', ')}
    </p>
  </>
);
